{"cosmos-sdk": {"branch": "main", "description": ":chains: A Framework for Building High Value Public Blockchains :sparkles:", "homepage": "https://cosmos.network/", "owner": "cosmos", "repo": "cosmos-sdk", "rev": "b6c77e6c819f8a51166649eaef125d1bfb276f04", "sha256": "09ns4yfxyfi7c7n5g69zv32m1rdssdl48c1akmv1y2vz6ayz4v02", "type": "tarball", "url": "https://github.com/cosmos/cosmos-sdk/archive/b6c77e6c819f8a51166649eaef125d1bfb276f04.tar.gz", "url_template": "https://github.com/<owner>/<repo>/archive/<rev>.tar.gz"}, "gomod2nix": {"branch": "master", "description": "Convert applications using Go modules to Nix expressions", "homepage": null, "owner": "tweag", "repo": "gomod2nix", "rev": "40d32f82fc60d66402eb0972e6e368aeab3faf58", "sha256": "0li17ynbg2wg0xqy655m5rmcw905sbv7d4ir35z7s5pg1yhhzxkp", "type": "tarball", "url": "https://github.com/tweag/gomod2nix/archive/40d32f82fc60d66402eb0972e6e368aeab3faf58.tar.gz", "url_template": "https://github.com/<owner>/<repo>/archive/<rev>.tar.gz"}, "niv": {"branch": "master", "description": "Easy dependency management for Nix projects", "homepage": "https://github.com/nmattia/niv", "owner": "nmattia", "repo": "niv", "rev": "e0ca65c81a2d7a4d82a189f1e23a48d59ad42070", "sha256": "1pq9nh1d8nn3xvbdny8fafzw87mj7gsmp6pxkdl65w2g18rmcmzx", "type": "tarball", "url": "https://github.com/nmattia/niv/archive/e0ca65c81a2d7a4d82a189f1e23a48d59ad42070.tar.gz", "url_template": "https://github.com/<owner>/<repo>/archive/<rev>.tar.gz"}, "nixpkgs": {"branch": "master", "description": "Nix Packages collection", "homepage": "", "owner": "NixOS", "repo": "nixpkgs", "rev": "ea2c6a6dda1aa35a80139a512a9dee375b0946e7", "sha256": "0nd4nn9ryqa80ssw3j2kmr8wa73p2xz1hyxp280cdzqlmdfgw1lm", "type": "tarball", "url": "https://github.com/NixOS/nixpkgs/archive/ea2c6a6dda1aa35a80139a512a9dee375b0946e7.tar.gz", "url_template": "https://github.com/<owner>/<repo>/archive/<rev>.tar.gz"}}