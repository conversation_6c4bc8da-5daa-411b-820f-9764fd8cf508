queue_rules:
  - name: default
    conditions:
      - "#approved-reviews-by>1"

pull_request_rules:
  - name: automerge to main with label "automerge" and branch protection passing
    conditions:
      - "#approved-reviews-by>1"
      - base=main
      - label=automerge
    actions:
      queue:
        name: default
        method: squash
        commit_message_template: |
          {{ title }} (#{{ number }})
          {{ body }}
  - name: backport patches to main branch
    conditions:
      - label=backport/main
    actions:
      backport:
        branches:
          - main
  - name: backport patches to v0.20.x branch
    conditions:
      - base=main
      - label=backport/0.20.x
    actions:
      backport:
        branches:
          - release/v0.20.x
  - name: backport patches to v0.19.x branch
    conditions:
      - base=main
      - label=backport/0.19.x
    actions:
      backport:
        branches:
          - release/v0.19.x
