<!--
parent:
  order: false
-->

<div align="center">
  <h1> Ethermint </h1>
</div>

![banner](docs/ethermint.jpg)

<div align="center">
  <a href="https://github.com/evmos/ethermint/releases/latest">
    <img alt="Version" src="https://img.shields.io/github/tag/evmos/ethermint.svg" />
  </a>
  <a href="https://github.com/evmos/ethermint/blob/main/LICENSE">
    <img alt="License: LGPL-v3" src="https://img.shields.io/github/license/evmos/ethermint.svg" />
  </a>
  <a href="https://pkg.go.dev/github.com/evmos/ethermint">
    <img alt="GoDoc" src="https://godoc.org/github.com/evmos/ethermint?status.svg" />
  </a>
  <a href="https://goreportcard.com/report/github.com/evmos/ethermint">
    <img alt="Go report card" src="https://goreportcard.com/badge/github.com/evmos/ethermint"/>
  </a>
  <a href="https://bestpractices.coreinfrastructure.org/projects/5018">
    <img alt="Lines of code" src="https://img.shields.io/tokei/lines/github/evmos/ethermint">
  </a>
</div>
<div align="center">
  <a href="https://discord.gg/trje9XuAmy">
    <img alt="Discord" src="https://img.shields.io/discord/809048090249134080.svg" />
  </a>
  <a href="https://github.com/evmos/ethermint/actions?query=branch%3Amain+workflow%3ALint">
    <img alt="Lint Status" src="https://github.com/evmos/ethermint/actions/workflows/lint.yml/badge.svg?branch=main" />
  </a>
  <a href="https://codecov.io/gh/tharsis/ethermint">
    <img alt="Code Coverage" src="https://codecov.io/gh/evmos/ethermint/branch/main/graph/badge.svg" />
  </a>
</div>

> [!WARNING] 
> Evmos, the team behind Ethermint, has fully shifted its focus to [Evmos](https://github.com/evmos/evmos), where we
> continue to build interoperability solutions for the future!
> As a result, this repository is no longer maintained for that reason and all relevant code has been migrated.
>
> **NOTE: If you are interested in using this software** email us at [<EMAIL>](mailto:<EMAIL>) with copy to [<EMAIL>](mailto:<EMAIL>)

## About 

Ethermint is a scalable and interoperable Ethereum library, built on Proof-of-Stake with fast-finality using the [Cosmos SDK](https://github.com/cosmos/cosmos-sdk/) which runs on top of [Tendermint Core](https://github.com/tendermint/tendermint) consensus engine.

## Careers

See our open positions on [Evmos Careers](https://evmos.org/careers).


## Community

The following chat channels and forums are a great spot to ask questions about Ethermint:

- [Evmos Twitter](https://twitter.com/EvmosOrg)
- [Evmos Discord](https://discord.gg/evmos)
- [Evmos Telegram](https://t.me/EvmosOrg)
- [Altiplanic Twitter](https://twitter.com/Altiplanic_io)



