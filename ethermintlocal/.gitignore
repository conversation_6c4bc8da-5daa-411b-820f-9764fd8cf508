# OS
.DS_Store
*.swp
*.swo
*.swl
*.swm
*.swn
.vscode
.idea
*.pyc
*.exe
*.exe~
*.dll
*.so
*.dylib
.dccache

# Build
*.test
.glide/
vendor
build
bin
tools/bin/*
docs/_build
docs/tutorial
docs/node_modules
docs/modules
dist
tools-stamp
docs-tools-stamp
proto-tools-stamp
golangci-lint
keyring_test_cosmos
./**/node_modules
./**/dist
secret.yml
artifacts/*
tmp-swagger-gen
github.com/
# vue/

# Local docker volume mappings
localnet-setup
.testnets

# Testing
coverage.txt
*.out
sim_log_file
tests/**/tmp/*
yarn.lock

# Vagrant
.vagrant/
*.box
*.log
vagrant

# IDE
.idea/
*.iml
*.code-workspace

# Graphviz
dependency-graph.png

# Latex
*.aux
*.out
*.synctex.gz

# Contracts
*.bin
*.abi

# Node.js
**/node_modules


# OpenZeppelin contracts
contracts/@openzeppelin/*
