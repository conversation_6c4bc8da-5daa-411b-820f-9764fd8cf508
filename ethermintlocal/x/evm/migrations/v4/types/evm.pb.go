// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: ethermint/evm/v1/evm.proto

package types

import (
	fmt "fmt"
	github_com_cosmos_cosmos_sdk_types "github.com/cosmos/cosmos-sdk/types"
	_ "github.com/cosmos/gogoproto/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

// V4Params defines the EVM module parameters
type V4Params struct {
	// evm_denom represents the token denomination used to run the EVM V4State
	// transitions.
	EvmDenom string `protobuf:"bytes,1,opt,name=evm_denom,json=evmDenom,proto3" json:"evm_denom,omitempty" yaml:"evm_denom"`
	// enable_create toggles V4State transitions that use the vm.Create function
	EnableCreate bool `protobuf:"varint,2,opt,name=enable_create,json=enableCreate,proto3" json:"enable_create,omitempty" yaml:"enable_create"`
	// enable_call toggles V4State transitions that use the vm.Call function
	EnableCall bool `protobuf:"varint,3,opt,name=enable_call,json=enableCall,proto3" json:"enable_call,omitempty" yaml:"enable_call"`
	// extra_eips defines the additional EIPs for the vm.Config
	ExtraEIPs ExtraEIPs `protobuf:"bytes,4,opt,name=extra_eips,json=extraEips,proto3" json:"extra_eips" yaml:"extra_eips"`
	// chain_config defines the EVM chain configuration parameters
	V4ChainConfig V4ChainConfig `protobuf:"bytes,5,opt,name=chain_config,json=V4ChainConfig,proto3" json:"chain_config" yaml:"chain_config"`
	// allow_unprotected_txs defines if replay-protected (i.e non EIP155
	// signed) transactions can be executed on the V4State machine.
	AllowUnprotectedTxs bool `protobuf:"varint,6,opt,name=allow_unprotected_txs,json=allowUnprotectedTxs,proto3" json:"allow_unprotected_txs,omitempty"`
}

func (m *V4Params) Reset()         { *m = V4Params{} }
func (m *V4Params) String() string { return proto.CompactTextString(m) }
func (*V4Params) ProtoMessage()    {}
func (*V4Params) Descriptor() ([]byte, []int) {
	return fileDescriptor_d21ecc92c8c8583e, []int{0}
}
func (m *V4Params) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *V4Params) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_V4Params.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *V4Params) XXX_Merge(src proto.Message) {
	xxx_messageInfo_V4Params.Merge(m, src)
}
func (m *V4Params) XXX_Size() int {
	return m.Size()
}
func (m *V4Params) XXX_DiscardUnknown() {
	xxx_messageInfo_V4Params.DiscardUnknown(m)
}

var xxx_messageInfo_V4Params proto.InternalMessageInfo

func (m *V4Params) GetEvmDenom() string {
	if m != nil {
		return m.EvmDenom
	}
	return ""
}

func (m *V4Params) GetEnableCreate() bool {
	if m != nil {
		return m.EnableCreate
	}
	return false
}

func (m *V4Params) GetEnableCall() bool {
	if m != nil {
		return m.EnableCall
	}
	return false
}

func (m *V4Params) GetExtraEIPs() ExtraEIPs {
	if m != nil {
		return m.ExtraEIPs
	}
	return ExtraEIPs{}
}

func (m *V4Params) GetV4ChainConfig() V4ChainConfig {
	if m != nil {
		return m.V4ChainConfig
	}
	return V4ChainConfig{}
}

func (m *V4Params) GetAllowUnprotectedTxs() bool {
	if m != nil {
		return m.AllowUnprotectedTxs
	}
	return false
}

// ExtraEIPs represents extra EIPs for the vm.Config
type ExtraEIPs struct {
	// eips defines the additional EIPs for the vm.Config
	EIPs []int64 `protobuf:"varint,1,rep,packed,name=eips,proto3" json:"eips,omitempty" yaml:"eips"`
}

func (m *ExtraEIPs) Reset()         { *m = ExtraEIPs{} }
func (m *ExtraEIPs) String() string { return proto.CompactTextString(m) }
func (*ExtraEIPs) ProtoMessage()    {}
func (*ExtraEIPs) Descriptor() ([]byte, []int) {
	return fileDescriptor_d21ecc92c8c8583e, []int{1}
}
func (m *ExtraEIPs) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExtraEIPs) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExtraEIPs.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExtraEIPs) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExtraEIPs.Merge(m, src)
}
func (m *ExtraEIPs) XXX_Size() int {
	return m.Size()
}
func (m *ExtraEIPs) XXX_DiscardUnknown() {
	xxx_messageInfo_ExtraEIPs.DiscardUnknown(m)
}

var xxx_messageInfo_ExtraEIPs proto.InternalMessageInfo

func (m *ExtraEIPs) GetEIPs() []int64 {
	if m != nil {
		return m.EIPs
	}
	return nil
}

// V4ChainConfig defines the Ethereum V4ChainConfig parameters using *sdk.Int values
// instead of *big.Int.
type V4ChainConfig struct {
	// homestead_block switch (nil no fork, 0 = already homestead)
	HomesteadBlock *github_com_cosmos_cosmos_sdk_types.Int `protobuf:"bytes,1,opt,name=homestead_block,json=homesteadBlock,proto3,customtype=github.com/cosmos/cosmos-sdk/types.Int" json:"homestead_block,omitempty" yaml:"homestead_block"`
	// dao_fork_block corresponds to TheDAO hard-fork switch block (nil no fork)
	DAOForkBlock *github_com_cosmos_cosmos_sdk_types.Int `protobuf:"bytes,2,opt,name=dao_fork_block,json=daoForkBlock,proto3,customtype=github.com/cosmos/cosmos-sdk/types.Int" json:"dao_fork_block,omitempty" yaml:"dao_fork_block"`
	// dao_fork_support defines whether the nodes supports or opposes the DAO hard-fork
	DAOForkSupport bool `protobuf:"varint,3,opt,name=dao_fork_support,json=daoForkSupport,proto3" json:"dao_fork_support,omitempty" yaml:"dao_fork_support"`
	// eip150_block: EIP150 implements the Gas price changes
	// (https://github.com/ethereum/EIPs/issues/150) EIP150 HF block (nil no fork)
	EIP150Block *github_com_cosmos_cosmos_sdk_types.Int `protobuf:"bytes,4,opt,name=eip150_block,json=eip150Block,proto3,customtype=github.com/cosmos/cosmos-sdk/types.Int" json:"eip150_block,omitempty" yaml:"eip150_block"`
	// eip150_hash: EIP150 HF hash (needed for header only clients as only gas pricing changed)
	EIP150Hash string `protobuf:"bytes,5,opt,name=eip150_hash,json=eip150Hash,proto3" json:"eip150_hash,omitempty" yaml:"byzantium_block"`
	// eip155_block: EIP155Block HF block
	EIP155Block *github_com_cosmos_cosmos_sdk_types.Int `protobuf:"bytes,6,opt,name=eip155_block,json=eip155Block,proto3,customtype=github.com/cosmos/cosmos-sdk/types.Int" json:"eip155_block,omitempty" yaml:"eip155_block"`
	// eip158_block: EIP158 HF block
	EIP158Block *github_com_cosmos_cosmos_sdk_types.Int `protobuf:"bytes,7,opt,name=eip158_block,json=eip158Block,proto3,customtype=github.com/cosmos/cosmos-sdk/types.Int" json:"eip158_block,omitempty" yaml:"eip158_block"`
	// byzantium_block: Byzantium switch block (nil no fork, 0 = already on byzantium)
	ByzantiumBlock *github_com_cosmos_cosmos_sdk_types.Int `protobuf:"bytes,8,opt,name=byzantium_block,json=byzantiumBlock,proto3,customtype=github.com/cosmos/cosmos-sdk/types.Int" json:"byzantium_block,omitempty" yaml:"byzantium_block"`
	// constantinople_block: Constantinople switch block (nil no fork, 0 = already activated)
	ConstantinopleBlock *github_com_cosmos_cosmos_sdk_types.Int `protobuf:"bytes,9,opt,name=constantinople_block,json=constantinopleBlock,proto3,customtype=github.com/cosmos/cosmos-sdk/types.Int" json:"constantinople_block,omitempty" yaml:"constantinople_block"`
	// petersburg_block: Petersburg switch block (nil same as Constantinople)
	PetersburgBlock *github_com_cosmos_cosmos_sdk_types.Int `protobuf:"bytes,10,opt,name=petersburg_block,json=petersburgBlock,proto3,customtype=github.com/cosmos/cosmos-sdk/types.Int" json:"petersburg_block,omitempty" yaml:"petersburg_block"`
	// istanbul_block: Istanbul switch block (nil no fork, 0 = already on istanbul)
	IstanbulBlock *github_com_cosmos_cosmos_sdk_types.Int `protobuf:"bytes,11,opt,name=istanbul_block,json=istanbulBlock,proto3,customtype=github.com/cosmos/cosmos-sdk/types.Int" json:"istanbul_block,omitempty" yaml:"istanbul_block"`
	// muir_glacier_block: Eip-2384 (bomb delay) switch block (nil no fork, 0 = already activated)
	MuirGlacierBlock *github_com_cosmos_cosmos_sdk_types.Int `protobuf:"bytes,12,opt,name=muir_glacier_block,json=muirGlacierBlock,proto3,customtype=github.com/cosmos/cosmos-sdk/types.Int" json:"muir_glacier_block,omitempty" yaml:"muir_glacier_block"`
	// berlin_block: Berlin switch block (nil = no fork, 0 = already on berlin)
	BerlinBlock *github_com_cosmos_cosmos_sdk_types.Int `protobuf:"bytes,13,opt,name=berlin_block,json=berlinBlock,proto3,customtype=github.com/cosmos/cosmos-sdk/types.Int" json:"berlin_block,omitempty" yaml:"berlin_block"`
	// london_block: London switch block (nil = no fork, 0 = already on london)
	LondonBlock *github_com_cosmos_cosmos_sdk_types.Int `protobuf:"bytes,17,opt,name=london_block,json=londonBlock,proto3,customtype=github.com/cosmos/cosmos-sdk/types.Int" json:"london_block,omitempty" yaml:"london_block"`
	// arrow_glacier_block: Eip-4345 (bomb delay) switch block (nil = no fork, 0 = already activated)
	ArrowGlacierBlock *github_com_cosmos_cosmos_sdk_types.Int `protobuf:"bytes,18,opt,name=arrow_glacier_block,json=arrowGlacierBlock,proto3,customtype=github.com/cosmos/cosmos-sdk/types.Int" json:"arrow_glacier_block,omitempty" yaml:"arrow_glacier_block"`
	// gray_glacier_block: EIP-5133 (bomb delay) switch block (nil = no fork, 0 = already activated)
	GrayGlacierBlock *github_com_cosmos_cosmos_sdk_types.Int `protobuf:"bytes,20,opt,name=gray_glacier_block,json=grayGlacierBlock,proto3,customtype=github.com/cosmos/cosmos-sdk/types.Int" json:"gray_glacier_block,omitempty" yaml:"gray_glacier_block"`
	// merge_netsplit_block: Virtual fork after The Merge to use as a network splitter
	MergeNetsplitBlock *github_com_cosmos_cosmos_sdk_types.Int `protobuf:"bytes,21,opt,name=merge_netsplit_block,json=mergeNetsplitBlock,proto3,customtype=github.com/cosmos/cosmos-sdk/types.Int" json:"merge_netsplit_block,omitempty" yaml:"merge_netsplit_block"`
	// shanghai_block switch block (nil = no fork, 0 = already on shanghai)
	ShanghaiBlock *github_com_cosmos_cosmos_sdk_types.Int `protobuf:"bytes,22,opt,name=shanghai_block,json=shanghaiBlock,proto3,customtype=github.com/cosmos/cosmos-sdk/types.Int" json:"shanghai_block,omitempty" yaml:"shanghai_block"`
	// cancun_block switch block (nil = no fork, 0 = already on cancun)
	CancunBlock *github_com_cosmos_cosmos_sdk_types.Int `protobuf:"bytes,23,opt,name=cancun_block,json=cancunBlock,proto3,customtype=github.com/cosmos/cosmos-sdk/types.Int" json:"cancun_block,omitempty" yaml:"cancun_block"`
}

func (m *V4ChainConfig) Reset()         { *m = V4ChainConfig{} }
func (m *V4ChainConfig) String() string { return proto.CompactTextString(m) }
func (*V4ChainConfig) ProtoMessage()    {}
func (*V4ChainConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_d21ecc92c8c8583e, []int{2}
}
func (m *V4ChainConfig) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *V4ChainConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_V4ChainConfig.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *V4ChainConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_V4ChainConfig.Merge(m, src)
}
func (m *V4ChainConfig) XXX_Size() int {
	return m.Size()
}
func (m *V4ChainConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_V4ChainConfig.DiscardUnknown(m)
}

var xxx_messageInfo_V4ChainConfig proto.InternalMessageInfo

func (m *V4ChainConfig) GetDAOForkSupport() bool {
	if m != nil {
		return m.DAOForkSupport
	}
	return false
}

func (m *V4ChainConfig) GetEIP150Hash() string {
	if m != nil {
		return m.EIP150Hash
	}
	return ""
}

// V4State represents a single Storage key value pair item.
type V4State struct {
	// key is the stored key
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// value is the stored value for the given key
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *V4State) Reset()         { *m = V4State{} }
func (m *V4State) String() string { return proto.CompactTextString(m) }
func (*V4State) ProtoMessage()    {}
func (*V4State) Descriptor() ([]byte, []int) {
	return fileDescriptor_d21ecc92c8c8583e, []int{3}
}
func (m *V4State) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *V4State) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_V4State.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *V4State) XXX_Merge(src proto.Message) {
	xxx_messageInfo_V4State.Merge(m, src)
}
func (m *V4State) XXX_Size() int {
	return m.Size()
}
func (m *V4State) XXX_DiscardUnknown() {
	xxx_messageInfo_V4State.DiscardUnknown(m)
}

var xxx_messageInfo_V4State proto.InternalMessageInfo

func (m *V4State) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *V4State) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

// TransactionV4Logs define the V4Logs generated from a transaction execution
// with a given hash. It it used for import/export data as transactions are not
// persisted on blockchain V4State after an upgrade.
type TransactionV4Logs struct {
	// hash of the transaction
	Hash string `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	// V4Logs is an array of V4Logs for the given transaction hash
	V4Logs []*V4Log `protobuf:"bytes,2,rep,name=V4Logs,proto3" json:"V4Logs,omitempty"`
}

func (m *TransactionV4Logs) Reset()         { *m = TransactionV4Logs{} }
func (m *TransactionV4Logs) String() string { return proto.CompactTextString(m) }
func (*TransactionV4Logs) ProtoMessage()    {}
func (*TransactionV4Logs) Descriptor() ([]byte, []int) {
	return fileDescriptor_d21ecc92c8c8583e, []int{4}
}
func (m *TransactionV4Logs) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TransactionV4Logs) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TransactionV4Logs.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TransactionV4Logs) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransactionV4Logs.Merge(m, src)
}
func (m *TransactionV4Logs) XXX_Size() int {
	return m.Size()
}
func (m *TransactionV4Logs) XXX_DiscardUnknown() {
	xxx_messageInfo_TransactionV4Logs.DiscardUnknown(m)
}

var xxx_messageInfo_TransactionV4Logs proto.InternalMessageInfo

func (m *TransactionV4Logs) GetHash() string {
	if m != nil {
		return m.Hash
	}
	return ""
}

func (m *TransactionV4Logs) GetV4Logs() []*V4Log {
	if m != nil {
		return m.V4Logs
	}
	return nil
}

// V4Log represents an protobuf compatible Ethereum V4Log that defines a contract
// V4Log event. These events are generated by the V4Log opcode and stored/indexed by
// the node.
//
// NOTE: address, topics and data are consensus fields. The rest of the fields
// are derived, i.e. filled in by the nodes, but not secured by consensus.
type V4Log struct {
	// address of the contract that generated the event
	Address string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	// topics is a list of topics provided by the contract.
	Topics []string `protobuf:"bytes,2,rep,name=topics,proto3" json:"topics,omitempty"`
	// data which is supplied by the contract, usually ABI-encoded
	Data []byte `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	// block_number of the block in which the transaction was included
	BlockNumber uint64 `protobuf:"varint,4,opt,name=block_number,json=blockNumber,proto3" json:"blockNumber"`
	// tx_hash is the transaction hash
	TxHash string `protobuf:"bytes,5,opt,name=tx_hash,json=txHash,proto3" json:"transactionHash"`
	// tx_index of the transaction in the block
	TxIndex uint64 `protobuf:"varint,6,opt,name=tx_index,json=txIndex,proto3" json:"transactionIndex"`
	// block_hash of the block in which the transaction was included
	BlockHash string `protobuf:"bytes,7,opt,name=block_hash,json=blockHash,proto3" json:"blockHash"`
	// index of the V4Log in the block
	Index uint64 `protobuf:"varint,8,opt,name=index,proto3" json:"V4LogIndex"`
	// removed is true if this V4Log was reverted due to a chain
	// reorganisation. You must pay attention to this field if you receive V4Logs
	// through a filter query.
	Removed bool `protobuf:"varint,9,opt,name=removed,proto3" json:"removed,omitempty"`
}

func (m *V4Log) Reset()         { *m = V4Log{} }
func (m *V4Log) String() string { return proto.CompactTextString(m) }
func (*V4Log) ProtoMessage()    {}
func (*V4Log) Descriptor() ([]byte, []int) {
	return fileDescriptor_d21ecc92c8c8583e, []int{5}
}
func (m *V4Log) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *V4Log) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_V4Log.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *V4Log) XXX_Merge(src proto.Message) {
	xxx_messageInfo_V4Log.Merge(m, src)
}
func (m *V4Log) XXX_Size() int {
	return m.Size()
}
func (m *V4Log) XXX_DiscardUnknown() {
	xxx_messageInfo_V4Log.DiscardUnknown(m)
}

var xxx_messageInfo_V4Log proto.InternalMessageInfo

func (m *V4Log) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *V4Log) GetTopics() []string {
	if m != nil {
		return m.Topics
	}
	return nil
}

func (m *V4Log) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *V4Log) GetBlockNumber() uint64 {
	if m != nil {
		return m.BlockNumber
	}
	return 0
}

func (m *V4Log) GetTxHash() string {
	if m != nil {
		return m.TxHash
	}
	return ""
}

func (m *V4Log) GetTxIndex() uint64 {
	if m != nil {
		return m.TxIndex
	}
	return 0
}

func (m *V4Log) GetBlockHash() string {
	if m != nil {
		return m.BlockHash
	}
	return ""
}

func (m *V4Log) GetIndex() uint64 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *V4Log) GetRemoved() bool {
	if m != nil {
		return m.Removed
	}
	return false
}

// V4TxResult stores results of Tx execution.
type V4TxResult struct {
	// contract_address contains the ethereum address of the created contract (if
	// any). If the V4State transition is an evm.Call, the contract address will be
	// empty.
	ContractAddress string `protobuf:"bytes,1,opt,name=contract_address,json=contractAddress,proto3" json:"contract_address,omitempty" yaml:"contract_address"`
	// bloom represents the bloom filter bytes
	Bloom []byte `protobuf:"bytes,2,opt,name=bloom,proto3" json:"bloom,omitempty"`
	// tx_V4Logs contains the transaction hash and the proto-compatible ethereum
	// V4Logs.
	TxV4Logs TransactionV4Logs `protobuf:"bytes,3,opt,name=tx_V4Logs,json=txV4Logs,proto3" json:"tx_V4Logs" yaml:"tx_V4Logs"`
	// ret defines the bytes from the execution.
	Ret []byte `protobuf:"bytes,4,opt,name=ret,proto3" json:"ret,omitempty"`
	// reverted flag is set to true when the call has been reverted
	Reverted bool `protobuf:"varint,5,opt,name=reverted,proto3" json:"reverted,omitempty"`
	// gas_used notes the amount of gas consumed while execution
	GasUsed uint64 `protobuf:"varint,6,opt,name=gas_used,json=gasUsed,proto3" json:"gas_used,omitempty"`
}

func (m *V4TxResult) Reset()         { *m = V4TxResult{} }
func (m *V4TxResult) String() string { return proto.CompactTextString(m) }
func (*V4TxResult) ProtoMessage()    {}
func (*V4TxResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_d21ecc92c8c8583e, []int{6}
}
func (m *V4TxResult) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *V4TxResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_V4TxResult.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *V4TxResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_V4TxResult.Merge(m, src)
}
func (m *V4TxResult) XXX_Size() int {
	return m.Size()
}
func (m *V4TxResult) XXX_DiscardUnknown() {
	xxx_messageInfo_V4TxResult.DiscardUnknown(m)
}

var xxx_messageInfo_V4TxResult proto.InternalMessageInfo

// V4AccessTuple is the element type of an access list.
type V4AccessTuple struct {
	// address is a hex formatted ethereum address
	Address string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	// storage_keys are hex formatted hashes of the storage keys
	StorageKeys []string `protobuf:"bytes,2,rep,name=storage_keys,json=storageKeys,proto3" json:"storageKeys"`
}

func (m *V4AccessTuple) Reset()         { *m = V4AccessTuple{} }
func (m *V4AccessTuple) String() string { return proto.CompactTextString(m) }
func (*V4AccessTuple) ProtoMessage()    {}
func (*V4AccessTuple) Descriptor() ([]byte, []int) {
	return fileDescriptor_d21ecc92c8c8583e, []int{7}
}
func (m *V4AccessTuple) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *V4AccessTuple) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_V4AccessTuple.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *V4AccessTuple) XXX_Merge(src proto.Message) {
	xxx_messageInfo_V4AccessTuple.Merge(m, src)
}
func (m *V4AccessTuple) XXX_Size() int {
	return m.Size()
}
func (m *V4AccessTuple) XXX_DiscardUnknown() {
	xxx_messageInfo_V4AccessTuple.DiscardUnknown(m)
}

var xxx_messageInfo_V4AccessTuple proto.InternalMessageInfo

// V4TraceConfig holds extra parameters to trace functions.
type V4TraceConfig struct {
	// tracer is a custom javascript tracer
	Tracer string `protobuf:"bytes,1,opt,name=tracer,proto3" json:"tracer,omitempty"`
	// timeout overrides the default timeout of 5 seconds for JavaScript-based tracing
	// calls
	Timeout string `protobuf:"bytes,2,opt,name=timeout,proto3" json:"timeout,omitempty"`
	// reexec defines the number of blocks the tracer is willing to go back
	Reexec uint64 `protobuf:"varint,3,opt,name=reexec,proto3" json:"reexec,omitempty"`
	// disable_stack switches stack capture
	DisableStack bool `protobuf:"varint,5,opt,name=disable_stack,json=disableStack,proto3" json:"disableStack"`
	// disable_storage switches storage capture
	DisableStorage bool `protobuf:"varint,6,opt,name=disable_storage,json=disableStorage,proto3" json:"disableStorage"`
	// debug can be used to print output during capture end
	Debug bool `protobuf:"varint,8,opt,name=debug,proto3" json:"debug,omitempty"`
	// limit defines the maximum length of output, but zero means unlimited
	Limit int32 `protobuf:"varint,9,opt,name=limit,proto3" json:"limit,omitempty"`
	// overrides can be used to execute a trace using future fork rules
	Overrides *V4ChainConfig `protobuf:"bytes,10,opt,name=overrides,proto3" json:"overrides,omitempty"`
	// enable_memory switches memory capture
	EnableMemory bool `protobuf:"varint,11,opt,name=enable_memory,json=enableMemory,proto3" json:"enableMemory"`
	// enable_return_data switches the capture of return data
	EnableReturnData bool `protobuf:"varint,12,opt,name=enable_return_data,json=enableReturnData,proto3" json:"enableReturnData"`
	// tracer_json_config configures the tracer using a JSON string
	TracerJsonConfig string `protobuf:"bytes,13,opt,name=tracer_json_config,json=tracerJsonConfig,proto3" json:"tracerConfig"`
}

func (m *V4TraceConfig) Reset()         { *m = V4TraceConfig{} }
func (m *V4TraceConfig) String() string { return proto.CompactTextString(m) }
func (*V4TraceConfig) ProtoMessage()    {}
func (*V4TraceConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_d21ecc92c8c8583e, []int{8}
}
func (m *V4TraceConfig) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *V4TraceConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_V4TraceConfig.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *V4TraceConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_V4TraceConfig.Merge(m, src)
}
func (m *V4TraceConfig) XXX_Size() int {
	return m.Size()
}
func (m *V4TraceConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_V4TraceConfig.DiscardUnknown(m)
}

var xxx_messageInfo_V4TraceConfig proto.InternalMessageInfo

func (m *V4TraceConfig) GetTracer() string {
	if m != nil {
		return m.Tracer
	}
	return ""
}

func (m *V4TraceConfig) GetTimeout() string {
	if m != nil {
		return m.Timeout
	}
	return ""
}

func (m *V4TraceConfig) GetReexec() uint64 {
	if m != nil {
		return m.Reexec
	}
	return 0
}

func (m *V4TraceConfig) GetDisableStack() bool {
	if m != nil {
		return m.DisableStack
	}
	return false
}

func (m *V4TraceConfig) GetDisableStorage() bool {
	if m != nil {
		return m.DisableStorage
	}
	return false
}

func (m *V4TraceConfig) GetDebug() bool {
	if m != nil {
		return m.Debug
	}
	return false
}

func (m *V4TraceConfig) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *V4TraceConfig) GetOverrides() *V4ChainConfig {
	if m != nil {
		return m.Overrides
	}
	return nil
}

func (m *V4TraceConfig) GetEnableMemory() bool {
	if m != nil {
		return m.EnableMemory
	}
	return false
}

func (m *V4TraceConfig) GetEnableReturnData() bool {
	if m != nil {
		return m.EnableReturnData
	}
	return false
}

func (m *V4TraceConfig) GetTracerJsonConfig() string {
	if m != nil {
		return m.TracerJsonConfig
	}
	return ""
}

func init() {
	proto.RegisterType((*V4Params)(nil), "ethermint.evm.v1.V4Params")
	proto.RegisterType((*ExtraEIPs)(nil), "ethermint.evm.v1.ExtraEIPs")
	proto.RegisterType((*V4ChainConfig)(nil), "ethermint.evm.v1.V4ChainConfig")
	proto.RegisterType((*V4State)(nil), "ethermint.evm.v1.V4State")
	proto.RegisterType((*TransactionV4Logs)(nil), "ethermint.evm.v1.TransactionV4Logs")
	proto.RegisterType((*V4Log)(nil), "ethermint.evm.v1.V4Log")
	proto.RegisterType((*V4TxResult)(nil), "ethermint.evm.v1.V4TxResult")
	proto.RegisterType((*V4AccessTuple)(nil), "ethermint.evm.v1.V4AccessTuple")
	proto.RegisterType((*V4TraceConfig)(nil), "ethermint.evm.v1.V4TraceConfig")
}

func init() { proto.RegisterFile("ethermint/evm/v1/evm.proto", fileDescriptor_d21ecc92c8c8583e) }

var fileDescriptor_d21ecc92c8c8583e = []byte{
	// 1644 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x58, 0x4f, 0x6f, 0xe3, 0xc6,
	0x15, 0xb7, 0x2d, 0xda, 0xa6, 0x46, 0xb2, 0x44, 0x8f, 0xb5, 0x5e, 0x65, 0x17, 0x35, 0x5d, 0x1e,
	0x02, 0x17, 0x4d, 0xec, 0xd8, 0x81, 0xd1, 0x45, 0x82, 0x16, 0x5d, 0xed, 0x3a, 0x89, 0xdd, 0x6d,
	0x6a, 0x8c, 0x1d, 0x14, 0x28, 0x50, 0x10, 0x23, 0x72, 0x42, 0x31, 0x26, 0x39, 0xc2, 0xcc, 0x50,
	0x2b, 0xb5, 0xfd, 0x00, 0x05, 0x7a, 0xe9, 0x27, 0x28, 0x72, 0xee, 0x27, 0x09, 0x7a, 0xda, 0x63,
	0xd1, 0x03, 0x51, 0x78, 0x6f, 0x3e, 0xea, 0x13, 0x14, 0xf3, 0x47, 0xd4, 0x1f, 0x1b, 0x6d, 0xad,
	0x93, 0xe7, 0xf7, 0xde, 0x9b, 0xdf, 0x6f, 0xde, 0x9b, 0x37, 0x9e, 0xa1, 0xc0, 0x33, 0x22, 0x7a,
	0x84, 0xa5, 0x71, 0x26, 0x8e, 0xc8, 0x20, 0x3d, 0x1a, 0x1c, 0xcb, 0x3f, 0x87, 0x7d, 0x46, 0x05,
	0x85, 0x4e, 0xe9, 0x3b, 0x94, 0xc6, 0xc1, 0xf1, 0xb3, 0x56, 0x44, 0x23, 0xaa, 0x9c, 0x47, 0x72,
	0xa4, 0xe3, 0xbc, 0xbf, 0x57, 0xc0, 0xc6, 0x25, 0x66, 0x38, 0xe5, 0xf0, 0x18, 0x54, 0xc9, 0x20,
	0xf5, 0x43, 0x92, 0xd1, 0xb4, 0xbd, 0xba, 0xbf, 0x7a, 0x50, 0xed, 0xb4, 0xc6, 0x85, 0xeb, 0x8c,
	0x70, 0x9a, 0x7c, 0xe6, 0x95, 0x2e, 0x0f, 0xd9, 0x64, 0x90, 0xbe, 0x96, 0x43, 0xf8, 0x73, 0xb0,
	0x45, 0x32, 0xdc, 0x4d, 0x88, 0x1f, 0x30, 0x82, 0x05, 0x69, 0xaf, 0xed, 0xaf, 0x1e, 0xd8, 0x9d,
	0xf6, 0xb8, 0x70, 0x5b, 0x66, 0xda, 0xac, 0xdb, 0x43, 0x75, 0x8d, 0x5f, 0x29, 0x08, 0x7f, 0x06,
	0x6a, 0x13, 0x3f, 0x4e, 0x92, 0x76, 0x45, 0x4d, 0xde, 0x1d, 0x17, 0x2e, 0x9c, 0x9f, 0x8c, 0x93,
	0xc4, 0x43, 0xc0, 0x4c, 0xc5, 0x49, 0x02, 0xbb, 0x00, 0x90, 0xa1, 0x60, 0xd8, 0x27, 0x71, 0x9f,
	0xb7, 0xad, 0xfd, 0xd5, 0x83, 0xda, 0xc9, 0xf3, 0xc3, 0xc5, 0x94, 0x0f, 0xcf, 0x64, 0xcc, 0xd9,
	0xf9, 0x25, 0xef, 0x7c, 0xf8, 0x43, 0xe1, 0xae, 0xdc, 0x16, 0x6e, 0xb5, 0x34, 0x8d, 0x0b, 0x77,
	0xdb, 0xa8, 0x94, 0x4c, 0x1e, 0xaa, 0x2a, 0x70, 0x16, 0xf7, 0x39, 0xfc, 0x3d, 0xa8, 0x07, 0x3d,
	0x1c, 0x67, 0x7e, 0x40, 0xb3, 0x6f, 0xe3, 0xa8, 0xbd, 0xae, 0x54, 0x7e, 0x74, 0x5f, 0xe5, 0x95,
	0x8c, 0x7a, 0xa5, 0x82, 0x3a, 0xcf, 0xa5, 0xce, 0xb8, 0x70, 0x77, 0x34, 0xf5, 0x2c, 0x81, 0x87,
	0x6a, 0xc1, 0x34, 0x12, 0x9e, 0x80, 0x27, 0x38, 0x49, 0xe8, 0x5b, 0x3f, 0xcf, 0xe4, 0x4e, 0x90,
	0x40, 0x90, 0xd0, 0x17, 0x43, 0xde, 0xde, 0x90, 0x55, 0x40, 0x3b, 0xca, 0xf9, 0xcd, 0xd4, 0x77,
	0x3d, 0xe4, 0xde, 0x0b, 0x30, 0x5d, 0x3f, 0xfc, 0x29, 0xb0, 0x54, 0xf6, 0xab, 0xfb, 0x95, 0x83,
	0x4a, 0xe7, 0xe9, 0x6d, 0xe1, 0x5a, 0x26, 0xaf, 0x9a, 0xc9, 0x4b, 0x65, 0xa4, 0x82, 0xbc, 0xbf,
	0x6d, 0x83, 0xda, 0xcc, 0x3a, 0x61, 0x0a, 0x9a, 0x3d, 0x9a, 0x12, 0x2e, 0x08, 0x0e, 0xfd, 0x6e,
	0x42, 0x83, 0x1b, 0xb3, 0xe3, 0xaf, 0xff, 0x55, 0xb8, 0x1f, 0x46, 0xb1, 0xe8, 0xe5, 0xdd, 0xc3,
	0x80, 0xa6, 0x47, 0x01, 0xe5, 0x29, 0xe5, 0xe6, 0xcf, 0xc7, 0x3c, 0xbc, 0x39, 0x12, 0xa3, 0x3e,
	0xe1, 0x87, 0xe7, 0x99, 0x18, 0x17, 0xee, 0xae, 0x56, 0x5a, 0xa0, 0xf2, 0x50, 0xa3, 0xb4, 0x74,
	0xa4, 0x01, 0x8e, 0x40, 0x23, 0xc4, 0xd4, 0xff, 0x96, 0xb2, 0x1b, 0xa3, 0xb6, 0xa6, 0xd4, 0xae,
	0xfe, 0x7f, 0xb5, 0xdb, 0xc2, 0xad, 0xbf, 0x7e, 0xf9, 0x9b, 0x2f, 0x28, 0xbb, 0x51, 0x9c, 0xe3,
	0xc2, 0x7d, 0xa2, 0xd5, 0xe7, 0x99, 0x3d, 0x54, 0x0f, 0x31, 0x2d, 0xc3, 0xe0, 0x6f, 0x81, 0x53,
	0x06, 0xf0, 0xbc, 0xdf, 0xa7, 0x4c, 0x98, 0x46, 0xfb, 0xf8, 0xb6, 0x70, 0x1b, 0x86, 0xf2, 0x4a,
	0x7b, 0xc6, 0x85, 0xfb, 0x74, 0x81, 0xd4, 0xcc, 0xf1, 0x50, 0xc3, 0xd0, 0x9a, 0x50, 0xc8, 0x41,
	0x9d, 0xc4, 0xfd, 0xe3, 0xd3, 0x4f, 0x4c, 0x46, 0x96, 0xca, 0xe8, 0xf2, 0x51, 0x19, 0xd5, 0xce,
	0xce, 0x2f, 0x8f, 0x4f, 0x3f, 0x99, 0x24, 0xb4, 0x53, 0x6e, 0x5c, 0x49, 0xeb, 0xa1, 0x9a, 0x86,
	0x3a, 0x9b, 0x73, 0x60, 0xa0, 0xdf, 0xc3, 0xbc, 0xa7, 0x7a, 0xb2, 0xda, 0x39, 0xb8, 0x2d, 0x5c,
	0xa0, 0x99, 0xbe, 0xc2, 0xbc, 0x37, 0xdd, 0x97, 0xee, 0xe8, 0x0f, 0x38, 0x13, 0x71, 0x9e, 0x4e,
	0xb8, 0x80, 0x9e, 0x2c, 0xa3, 0xca, 0xf5, 0x9f, 0x9a, 0xf5, 0x6f, 0x2c, 0xbd, 0xfe, 0xd3, 0x87,
	0xd6, 0x7f, 0x3a, 0xbf, 0x7e, 0x1d, 0x53, 0x8a, 0xbe, 0x30, 0xa2, 0x9b, 0x4b, 0x8b, 0xbe, 0x78,
	0x48, 0xf4, 0xc5, 0xbc, 0xa8, 0x8e, 0x91, 0xcd, 0xbe, 0x50, 0x89, 0xb6, 0xbd, 0x7c, 0xb3, 0xdf,
	0x2b, 0x6a, 0xa3, 0xb4, 0x68, 0xb9, 0x3f, 0x81, 0x56, 0x40, 0x33, 0x2e, 0xa4, 0x2d, 0xa3, 0xfd,
	0x84, 0x18, 0xcd, 0xaa, 0xd2, 0x3c, 0x7f, 0x94, 0xe6, 0x73, 0xf3, 0x7f, 0xe4, 0x01, 0x3e, 0x0f,
	0xed, 0xcc, 0x9b, 0xb5, 0x7a, 0x1f, 0x38, 0x7d, 0x22, 0x08, 0xe3, 0xdd, 0x9c, 0x45, 0x46, 0x19,
	0x28, 0xe5, 0xb3, 0x47, 0x29, 0x9b, 0x73, 0xb0, 0xc8, 0xe5, 0xa1, 0xe6, 0xd4, 0xa4, 0x15, 0xbf,
	0x03, 0x8d, 0x58, 0x2e, 0xa3, 0x9b, 0x27, 0x46, 0xaf, 0xa6, 0xf4, 0x5e, 0x3d, 0x4a, 0xcf, 0x1c,
	0xe6, 0x79, 0x26, 0x0f, 0x6d, 0x4d, 0x0c, 0x5a, 0x2b, 0x07, 0x30, 0xcd, 0x63, 0xe6, 0x47, 0x09,
	0x0e, 0x62, 0xc2, 0x8c, 0x5e, 0x5d, 0xe9, 0x7d, 0xf9, 0x28, 0xbd, 0x0f, 0xb4, 0xde, 0x7d, 0x36,
	0x0f, 0x39, 0xd2, 0xf8, 0xa5, 0xb6, 0x69, 0xd9, 0x10, 0xd4, 0xbb, 0x84, 0x25, 0x71, 0x66, 0x04,
	0xb7, 0x94, 0xe0, 0xcb, 0x47, 0x09, 0x9a, 0x3e, 0x9d, 0xe5, 0xf1, 0x50, 0x4d, 0xc3, 0x52, 0x25,
	0xa1, 0x59, 0x48, 0x27, 0x2a, 0xdb, 0xcb, 0xab, 0xcc, 0xf2, 0x78, 0xa8, 0xa6, 0xa1, 0x56, 0x19,
	0x82, 0x1d, 0xcc, 0x18, 0x7d, 0xbb, 0x50, 0x43, 0xa8, 0xc4, 0xbe, 0x7a, 0x94, 0xd8, 0x33, 0x2d,
	0xf6, 0x00, 0x9d, 0x87, 0xb6, 0x95, 0x75, 0xae, 0x8a, 0x39, 0x80, 0x11, 0xc3, 0xa3, 0x05, 0xe1,
	0xd6, 0xf2, 0x9b, 0x77, 0x9f, 0xcd, 0x43, 0x8e, 0x34, 0xce, 0xc9, 0xfe, 0x11, 0xb4, 0x52, 0xc2,
	0x22, 0xe2, 0x67, 0x44, 0xf0, 0x7e, 0x12, 0x0b, 0x23, 0xfc, 0x64, 0xf9, 0xf3, 0xf8, 0x10, 0x9f,
	0x87, 0xa0, 0x32, 0x7f, 0x6d, 0xac, 0xe5, 0xe1, 0xe0, 0x3d, 0x9c, 0x45, 0x3d, 0x1c, 0x1b, 0xd9,
	0xdd, 0xe5, 0x0f, 0xc7, 0x3c, 0x93, 0x87, 0xb6, 0x26, 0x86, 0xb2, 0x7f, 0x02, 0x9c, 0x05, 0xf9,
	0xa4, 0x7f, 0x9e, 0x2e, 0xdf, 0x3f, 0xb3, 0x3c, 0xf2, 0xe1, 0xa2, 0xa0, 0x52, 0xb9, 0xb0, 0xec,
	0x86, 0xd3, 0xbc, 0xb0, 0xec, 0xa6, 0xe3, 0x5c, 0x58, 0xb6, 0xe3, 0x6c, 0x5f, 0x58, 0xf6, 0x8e,
	0xd3, 0x42, 0x5b, 0x23, 0x9a, 0x50, 0x7f, 0xf0, 0xa9, 0x9e, 0x84, 0x6a, 0xe4, 0x2d, 0xe6, 0xe6,
	0x7f, 0x24, 0x6a, 0x04, 0x58, 0xe0, 0x64, 0xc4, 0x4d, 0xa9, 0x90, 0xa3, 0x0b, 0x38, 0x73, 0x6b,
	0x1f, 0x81, 0xf5, 0x2b, 0x21, 0xdf, 0x84, 0x0e, 0xa8, 0xdc, 0x90, 0x91, 0x7e, 0x8d, 0x20, 0x39,
	0x84, 0x2d, 0xb0, 0x3e, 0xc0, 0x49, 0xae, 0x1f, 0x97, 0x55, 0xa4, 0x81, 0x77, 0x09, 0x9a, 0xd7,
	0x0c, 0x67, 0x1c, 0x07, 0x22, 0xa6, 0xd9, 0x1b, 0x1a, 0x71, 0x08, 0x81, 0xa5, 0x6e, 0x45, 0x3d,
	0x57, 0x8d, 0xe1, 0x4f, 0x80, 0x95, 0xd0, 0x88, 0xb7, 0xd7, 0xf6, 0x2b, 0x07, 0xb5, 0x93, 0x27,
	0xf7, 0x5f, 0x6f, 0x6f, 0x68, 0x84, 0x54, 0x88, 0xf7, 0x8f, 0x35, 0x50, 0x79, 0x43, 0x23, 0xd8,
	0x06, 0x9b, 0x38, 0x0c, 0x19, 0xe1, 0xdc, 0x30, 0x4d, 0x20, 0xdc, 0x05, 0x1b, 0x82, 0xf6, 0xe3,
	0x40, 0xd3, 0x55, 0x91, 0x41, 0x52, 0x38, 0xc4, 0x02, 0xab, 0x77, 0x45, 0x1d, 0xa9, 0x31, 0x3c,
	0x01, 0x75, 0x95, 0x99, 0x9f, 0xe5, 0x69, 0x97, 0x30, 0xf5, 0x3c, 0xb0, 0x3a, 0xcd, 0xbb, 0xc2,
	0xad, 0x29, 0xfb, 0xd7, 0xca, 0x8c, 0x66, 0x01, 0xfc, 0x08, 0x6c, 0x8a, 0xe1, 0xec, 0xcd, 0xbe,
	0x73, 0x57, 0xb8, 0x4d, 0x31, 0x4d, 0x53, 0x5e, 0xdc, 0x68, 0x43, 0x0c, 0xd5, 0x05, 0x7e, 0x04,
	0x6c, 0x31, 0xf4, 0xe3, 0x2c, 0x24, 0x43, 0x75, 0x79, 0x5b, 0x9d, 0xd6, 0x5d, 0xe1, 0x3a, 0x33,
	0xe1, 0xe7, 0xd2, 0x87, 0x36, 0xc5, 0x50, 0x0d, 0xe0, 0x47, 0x00, 0xe8, 0x25, 0x29, 0x05, 0x7d,
	0xf5, 0x6e, 0xdd, 0x15, 0x6e, 0x55, 0x59, 0x15, 0xf7, 0x74, 0x08, 0x3d, 0xb0, 0xae, 0xb9, 0x6d,
	0xc5, 0x5d, 0xbf, 0x2b, 0x5c, 0x3b, 0xa1, 0x91, 0xe6, 0xd4, 0x2e, 0x59, 0x2a, 0x46, 0x52, 0x3a,
	0x20, 0xa1, 0xba, 0xdd, 0x6c, 0x34, 0x81, 0xde, 0x5f, 0xd6, 0x80, 0x7d, 0x3d, 0x44, 0x84, 0xe7,
	0x89, 0x80, 0x5f, 0x00, 0x27, 0xa0, 0x99, 0x60, 0x38, 0x10, 0xfe, 0x5c, 0x69, 0x3b, 0xcf, 0xa7,
	0x37, 0xcd, 0x62, 0x84, 0x87, 0x9a, 0x13, 0xd3, 0x4b, 0x53, 0xff, 0x16, 0x58, 0xef, 0x26, 0x94,
	0xa6, 0xaa, 0x13, 0xea, 0x48, 0x03, 0x88, 0x54, 0xd5, 0xd4, 0x2e, 0x57, 0xd4, 0x1b, 0xfd, 0xc7,
	0xf7, 0x77, 0x79, 0xa1, 0x55, 0x3a, 0xbb, 0xe6, 0x9d, 0xde, 0xd0, 0xda, 0x66, 0xbe, 0x27, 0x6b,
	0xab, 0x5a, 0xc9, 0x01, 0x15, 0x46, 0x84, 0xda, 0xb4, 0x3a, 0x92, 0x43, 0xf8, 0x0c, 0xd8, 0x8c,
	0x0c, 0x08, 0x13, 0x24, 0x54, 0x9b, 0x63, 0xa3, 0x12, 0xc3, 0x0f, 0x80, 0x1d, 0x61, 0xee, 0xe7,
	0x9c, 0x84, 0x7a, 0x27, 0xd0, 0x66, 0x84, 0xf9, 0x37, 0x9c, 0x84, 0x9f, 0x59, 0x7f, 0xfe, 0xde,
	0x5d, 0xf1, 0x30, 0xa8, 0xbd, 0x0c, 0x02, 0xc2, 0xf9, 0x75, 0xde, 0x4f, 0xc8, 0x7f, 0xe9, 0xb0,
	0x13, 0x50, 0xe7, 0x82, 0x32, 0x1c, 0x11, 0xff, 0x86, 0x8c, 0x4c, 0x9f, 0xe9, 0xae, 0x31, 0xf6,
	0x5f, 0x91, 0x11, 0x47, 0xb3, 0xc0, 0x48, 0x7c, 0x6f, 0x81, 0xda, 0x35, 0xc3, 0x01, 0x31, 0x2f,
	0x7c, 0xd9, 0xab, 0x12, 0x32, 0x23, 0x61, 0x90, 0xd4, 0x16, 0x71, 0x4a, 0x68, 0x2e, 0xcc, 0x79,
	0x9a, 0x40, 0x39, 0x83, 0x11, 0x32, 0x24, 0x81, 0x2a, 0xa3, 0x85, 0x0c, 0x82, 0xa7, 0x60, 0x2b,
	0x8c, 0xb9, 0xfa, 0x12, 0xe3, 0x02, 0x07, 0x37, 0x3a, 0xfd, 0x8e, 0x73, 0x57, 0xb8, 0x75, 0xe3,
	0xb8, 0x92, 0x76, 0x34, 0x87, 0xe0, 0xe7, 0xa0, 0x39, 0x9d, 0xa6, 0x56, 0xab, 0x3f, 0x6d, 0x3a,
	0xf0, 0xae, 0x70, 0x1b, 0x65, 0xa8, 0xf2, 0xa0, 0x05, 0x2c, 0x77, 0x3a, 0x24, 0xdd, 0x3c, 0x52,
	0xcd, 0x67, 0x23, 0x0d, 0xa4, 0x35, 0x89, 0xd3, 0x58, 0xa8, 0x66, 0x5b, 0x47, 0x1a, 0xc0, 0xcf,
	0x41, 0x95, 0x0e, 0x08, 0x63, 0x71, 0x48, 0xb8, 0x7a, 0xea, 0xfc, 0xaf, 0xaf, 0x34, 0x34, 0x8d,
	0x97, 0xc9, 0x99, 0xaf, 0xcc, 0x94, 0xa4, 0x94, 0x8d, 0xd4, 0xdb, 0xc5, 0x24, 0xa7, 0x1d, 0xbf,
	0x56, 0x76, 0x34, 0x87, 0x60, 0x07, 0x40, 0x33, 0x8d, 0x11, 0x91, 0xb3, 0xcc, 0x57, 0xe7, 0xbf,
	0xae, 0xe6, 0xaa, 0x53, 0xa8, 0xbd, 0x48, 0x39, 0x5f, 0x63, 0x81, 0xd1, 0x3d, 0x0b, 0xfc, 0x05,
	0x80, 0x7a, 0x4f, 0xfc, 0xef, 0x38, 0x2d, 0x3f, 0x33, 0xf5, 0xd3, 0x42, 0xe9, 0x6b, 0xaf, 0x59,
	0xb3, 0xa3, 0xd1, 0x05, 0xa7, 0x26, 0x8b, 0x0b, 0xcb, 0xb6, 0x9c, 0xf5, 0x0b, 0xcb, 0xde, 0x74,
	0xec, 0xb2, 0x7e, 0x26, 0x0b, 0xb4, 0x33, 0xc1, 0x33, 0xcb, 0xeb, 0xfc, 0xf2, 0x87, 0xdb, 0xbd,
	0xd5, 0x77, 0xb7, 0x7b, 0xab, 0xff, 0xbe, 0xdd, 0x5b, 0xfd, 0xeb, 0xfb, 0xbd, 0x95, 0x77, 0xef,
	0xf7, 0x56, 0xfe, 0xf9, 0x7e, 0x6f, 0xe5, 0x77, 0xb3, 0xf7, 0x03, 0x19, 0xc8, 0xeb, 0x61, 0xfa,
	0xd3, 0xc2, 0x50, 0xfd, 0xb8, 0xa0, 0xee, 0x88, 0xee, 0x86, 0xfa, 0xd1, 0xe0, 0xd3, 0xff, 0x04,
	0x00, 0x00, 0xff, 0xff, 0x68, 0xce, 0x8e, 0x23, 0x7a, 0x10, 0x00, 0x00,
}

func (m *V4Params) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *V4Params) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *V4Params) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.AllowUnprotectedTxs {
		i--
		if m.AllowUnprotectedTxs {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x30
	}
	{
		size, err := m.V4ChainConfig.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintEvm(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x2a
	{
		size, err := m.ExtraEIPs.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintEvm(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x22
	if m.EnableCall {
		i--
		if m.EnableCall {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x18
	}
	if m.EnableCreate {
		i--
		if m.EnableCreate {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if len(m.EvmDenom) > 0 {
		i -= len(m.EvmDenom)
		copy(dAtA[i:], m.EvmDenom)
		i = encodeVarintEvm(dAtA, i, uint64(len(m.EvmDenom)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ExtraEIPs) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExtraEIPs) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExtraEIPs) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.EIPs) > 0 {
		dAtA4 := make([]byte, len(m.EIPs)*10)
		var j3 int
		for _, num1 := range m.EIPs {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA4[j3] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j3++
			}
			dAtA4[j3] = uint8(num)
			j3++
		}
		i -= j3
		copy(dAtA[i:], dAtA4[:j3])
		i = encodeVarintEvm(dAtA, i, uint64(j3))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *V4ChainConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *V4ChainConfig) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *V4ChainConfig) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.CancunBlock != nil {
		{
			size := m.CancunBlock.Size()
			i -= size
			if _, err := m.CancunBlock.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintEvm(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xba
	}
	if m.ShanghaiBlock != nil {
		{
			size := m.ShanghaiBlock.Size()
			i -= size
			if _, err := m.ShanghaiBlock.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintEvm(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xb2
	}
	if m.MergeNetsplitBlock != nil {
		{
			size := m.MergeNetsplitBlock.Size()
			i -= size
			if _, err := m.MergeNetsplitBlock.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintEvm(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xaa
	}
	if m.GrayGlacierBlock != nil {
		{
			size := m.GrayGlacierBlock.Size()
			i -= size
			if _, err := m.GrayGlacierBlock.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintEvm(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xa2
	}
	if m.ArrowGlacierBlock != nil {
		{
			size := m.ArrowGlacierBlock.Size()
			i -= size
			if _, err := m.ArrowGlacierBlock.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintEvm(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x92
	}
	if m.LondonBlock != nil {
		{
			size := m.LondonBlock.Size()
			i -= size
			if _, err := m.LondonBlock.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintEvm(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x8a
	}
	if m.BerlinBlock != nil {
		{
			size := m.BerlinBlock.Size()
			i -= size
			if _, err := m.BerlinBlock.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintEvm(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x6a
	}
	if m.MuirGlacierBlock != nil {
		{
			size := m.MuirGlacierBlock.Size()
			i -= size
			if _, err := m.MuirGlacierBlock.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintEvm(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x62
	}
	if m.IstanbulBlock != nil {
		{
			size := m.IstanbulBlock.Size()
			i -= size
			if _, err := m.IstanbulBlock.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintEvm(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x5a
	}
	if m.PetersburgBlock != nil {
		{
			size := m.PetersburgBlock.Size()
			i -= size
			if _, err := m.PetersburgBlock.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintEvm(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	if m.ConstantinopleBlock != nil {
		{
			size := m.ConstantinopleBlock.Size()
			i -= size
			if _, err := m.ConstantinopleBlock.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintEvm(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x4a
	}
	if m.ByzantiumBlock != nil {
		{
			size := m.ByzantiumBlock.Size()
			i -= size
			if _, err := m.ByzantiumBlock.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintEvm(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	if m.EIP158Block != nil {
		{
			size := m.EIP158Block.Size()
			i -= size
			if _, err := m.EIP158Block.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintEvm(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	if m.EIP155Block != nil {
		{
			size := m.EIP155Block.Size()
			i -= size
			if _, err := m.EIP155Block.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintEvm(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if len(m.EIP150Hash) > 0 {
		i -= len(m.EIP150Hash)
		copy(dAtA[i:], m.EIP150Hash)
		i = encodeVarintEvm(dAtA, i, uint64(len(m.EIP150Hash)))
		i--
		dAtA[i] = 0x2a
	}
	if m.EIP150Block != nil {
		{
			size := m.EIP150Block.Size()
			i -= size
			if _, err := m.EIP150Block.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintEvm(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.DAOForkSupport {
		i--
		if m.DAOForkSupport {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x18
	}
	if m.DAOForkBlock != nil {
		{
			size := m.DAOForkBlock.Size()
			i -= size
			if _, err := m.DAOForkBlock.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintEvm(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.HomesteadBlock != nil {
		{
			size := m.HomesteadBlock.Size()
			i -= size
			if _, err := m.HomesteadBlock.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintEvm(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *V4State) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *V4State) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *V4State) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintEvm(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintEvm(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TransactionV4Logs) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransactionV4Logs) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TransactionV4Logs) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.V4Logs) > 0 {
		for iNdEx := len(m.V4Logs) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.V4Logs[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintEvm(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Hash) > 0 {
		i -= len(m.Hash)
		copy(dAtA[i:], m.Hash)
		i = encodeVarintEvm(dAtA, i, uint64(len(m.Hash)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *V4Log) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *V4Log) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *V4Log) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Removed {
		i--
		if m.Removed {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x48
	}
	if m.Index != 0 {
		i = encodeVarintEvm(dAtA, i, uint64(m.Index))
		i--
		dAtA[i] = 0x40
	}
	if len(m.BlockHash) > 0 {
		i -= len(m.BlockHash)
		copy(dAtA[i:], m.BlockHash)
		i = encodeVarintEvm(dAtA, i, uint64(len(m.BlockHash)))
		i--
		dAtA[i] = 0x3a
	}
	if m.TxIndex != 0 {
		i = encodeVarintEvm(dAtA, i, uint64(m.TxIndex))
		i--
		dAtA[i] = 0x30
	}
	if len(m.TxHash) > 0 {
		i -= len(m.TxHash)
		copy(dAtA[i:], m.TxHash)
		i = encodeVarintEvm(dAtA, i, uint64(len(m.TxHash)))
		i--
		dAtA[i] = 0x2a
	}
	if m.BlockNumber != 0 {
		i = encodeVarintEvm(dAtA, i, uint64(m.BlockNumber))
		i--
		dAtA[i] = 0x20
	}
	if len(m.Data) > 0 {
		i -= len(m.Data)
		copy(dAtA[i:], m.Data)
		i = encodeVarintEvm(dAtA, i, uint64(len(m.Data)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Topics) > 0 {
		for iNdEx := len(m.Topics) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Topics[iNdEx])
			copy(dAtA[i:], m.Topics[iNdEx])
			i = encodeVarintEvm(dAtA, i, uint64(len(m.Topics[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Address) > 0 {
		i -= len(m.Address)
		copy(dAtA[i:], m.Address)
		i = encodeVarintEvm(dAtA, i, uint64(len(m.Address)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *V4TxResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *V4TxResult) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *V4TxResult) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.GasUsed != 0 {
		i = encodeVarintEvm(dAtA, i, uint64(m.GasUsed))
		i--
		dAtA[i] = 0x30
	}
	if m.Reverted {
		i--
		if m.Reverted {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x28
	}
	if len(m.Ret) > 0 {
		i -= len(m.Ret)
		copy(dAtA[i:], m.Ret)
		i = encodeVarintEvm(dAtA, i, uint64(len(m.Ret)))
		i--
		dAtA[i] = 0x22
	}
	{
		size, err := m.TxV4Logs.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintEvm(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x1a
	if len(m.Bloom) > 0 {
		i -= len(m.Bloom)
		copy(dAtA[i:], m.Bloom)
		i = encodeVarintEvm(dAtA, i, uint64(len(m.Bloom)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.ContractAddress) > 0 {
		i -= len(m.ContractAddress)
		copy(dAtA[i:], m.ContractAddress)
		i = encodeVarintEvm(dAtA, i, uint64(len(m.ContractAddress)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *V4AccessTuple) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *V4AccessTuple) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *V4AccessTuple) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.StorageKeys) > 0 {
		for iNdEx := len(m.StorageKeys) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.StorageKeys[iNdEx])
			copy(dAtA[i:], m.StorageKeys[iNdEx])
			i = encodeVarintEvm(dAtA, i, uint64(len(m.StorageKeys[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Address) > 0 {
		i -= len(m.Address)
		copy(dAtA[i:], m.Address)
		i = encodeVarintEvm(dAtA, i, uint64(len(m.Address)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *V4TraceConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *V4TraceConfig) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *V4TraceConfig) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TracerJsonConfig) > 0 {
		i -= len(m.TracerJsonConfig)
		copy(dAtA[i:], m.TracerJsonConfig)
		i = encodeVarintEvm(dAtA, i, uint64(len(m.TracerJsonConfig)))
		i--
		dAtA[i] = 0x6a
	}
	if m.EnableReturnData {
		i--
		if m.EnableReturnData {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x60
	}
	if m.EnableMemory {
		i--
		if m.EnableMemory {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x58
	}
	if m.Overrides != nil {
		{
			size, err := m.Overrides.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEvm(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	if m.Limit != 0 {
		i = encodeVarintEvm(dAtA, i, uint64(m.Limit))
		i--
		dAtA[i] = 0x48
	}
	if m.Debug {
		i--
		if m.Debug {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x40
	}
	if m.DisableStorage {
		i--
		if m.DisableStorage {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x30
	}
	if m.DisableStack {
		i--
		if m.DisableStack {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x28
	}
	if m.Reexec != 0 {
		i = encodeVarintEvm(dAtA, i, uint64(m.Reexec))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Timeout) > 0 {
		i -= len(m.Timeout)
		copy(dAtA[i:], m.Timeout)
		i = encodeVarintEvm(dAtA, i, uint64(len(m.Timeout)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Tracer) > 0 {
		i -= len(m.Tracer)
		copy(dAtA[i:], m.Tracer)
		i = encodeVarintEvm(dAtA, i, uint64(len(m.Tracer)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintEvm(dAtA []byte, offset int, v uint64) int {
	offset -= sovEvm(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *V4Params) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.EvmDenom)
	if l > 0 {
		n += 1 + l + sovEvm(uint64(l))
	}
	if m.EnableCreate {
		n += 2
	}
	if m.EnableCall {
		n += 2
	}
	l = m.ExtraEIPs.Size()
	n += 1 + l + sovEvm(uint64(l))
	l = m.V4ChainConfig.Size()
	n += 1 + l + sovEvm(uint64(l))
	if m.AllowUnprotectedTxs {
		n += 2
	}
	return n
}

func (m *ExtraEIPs) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.EIPs) > 0 {
		l = 0
		for _, e := range m.EIPs {
			l += sovEvm(uint64(e))
		}
		n += 1 + sovEvm(uint64(l)) + l
	}
	return n
}

func (m *V4ChainConfig) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.HomesteadBlock != nil {
		l = m.HomesteadBlock.Size()
		n += 1 + l + sovEvm(uint64(l))
	}
	if m.DAOForkBlock != nil {
		l = m.DAOForkBlock.Size()
		n += 1 + l + sovEvm(uint64(l))
	}
	if m.DAOForkSupport {
		n += 2
	}
	if m.EIP150Block != nil {
		l = m.EIP150Block.Size()
		n += 1 + l + sovEvm(uint64(l))
	}
	l = len(m.EIP150Hash)
	if l > 0 {
		n += 1 + l + sovEvm(uint64(l))
	}
	if m.EIP155Block != nil {
		l = m.EIP155Block.Size()
		n += 1 + l + sovEvm(uint64(l))
	}
	if m.EIP158Block != nil {
		l = m.EIP158Block.Size()
		n += 1 + l + sovEvm(uint64(l))
	}
	if m.ByzantiumBlock != nil {
		l = m.ByzantiumBlock.Size()
		n += 1 + l + sovEvm(uint64(l))
	}
	if m.ConstantinopleBlock != nil {
		l = m.ConstantinopleBlock.Size()
		n += 1 + l + sovEvm(uint64(l))
	}
	if m.PetersburgBlock != nil {
		l = m.PetersburgBlock.Size()
		n += 1 + l + sovEvm(uint64(l))
	}
	if m.IstanbulBlock != nil {
		l = m.IstanbulBlock.Size()
		n += 1 + l + sovEvm(uint64(l))
	}
	if m.MuirGlacierBlock != nil {
		l = m.MuirGlacierBlock.Size()
		n += 1 + l + sovEvm(uint64(l))
	}
	if m.BerlinBlock != nil {
		l = m.BerlinBlock.Size()
		n += 1 + l + sovEvm(uint64(l))
	}
	if m.LondonBlock != nil {
		l = m.LondonBlock.Size()
		n += 2 + l + sovEvm(uint64(l))
	}
	if m.ArrowGlacierBlock != nil {
		l = m.ArrowGlacierBlock.Size()
		n += 2 + l + sovEvm(uint64(l))
	}
	if m.GrayGlacierBlock != nil {
		l = m.GrayGlacierBlock.Size()
		n += 2 + l + sovEvm(uint64(l))
	}
	if m.MergeNetsplitBlock != nil {
		l = m.MergeNetsplitBlock.Size()
		n += 2 + l + sovEvm(uint64(l))
	}
	if m.ShanghaiBlock != nil {
		l = m.ShanghaiBlock.Size()
		n += 2 + l + sovEvm(uint64(l))
	}
	if m.CancunBlock != nil {
		l = m.CancunBlock.Size()
		n += 2 + l + sovEvm(uint64(l))
	}
	return n
}

func (m *V4State) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovEvm(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovEvm(uint64(l))
	}
	return n
}

func (m *TransactionV4Logs) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Hash)
	if l > 0 {
		n += 1 + l + sovEvm(uint64(l))
	}
	if len(m.V4Logs) > 0 {
		for _, e := range m.V4Logs {
			l = e.Size()
			n += 1 + l + sovEvm(uint64(l))
		}
	}
	return n
}

func (m *V4Log) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Address)
	if l > 0 {
		n += 1 + l + sovEvm(uint64(l))
	}
	if len(m.Topics) > 0 {
		for _, s := range m.Topics {
			l = len(s)
			n += 1 + l + sovEvm(uint64(l))
		}
	}
	l = len(m.Data)
	if l > 0 {
		n += 1 + l + sovEvm(uint64(l))
	}
	if m.BlockNumber != 0 {
		n += 1 + sovEvm(uint64(m.BlockNumber))
	}
	l = len(m.TxHash)
	if l > 0 {
		n += 1 + l + sovEvm(uint64(l))
	}
	if m.TxIndex != 0 {
		n += 1 + sovEvm(uint64(m.TxIndex))
	}
	l = len(m.BlockHash)
	if l > 0 {
		n += 1 + l + sovEvm(uint64(l))
	}
	if m.Index != 0 {
		n += 1 + sovEvm(uint64(m.Index))
	}
	if m.Removed {
		n += 2
	}
	return n
}

func (m *V4TxResult) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ContractAddress)
	if l > 0 {
		n += 1 + l + sovEvm(uint64(l))
	}
	l = len(m.Bloom)
	if l > 0 {
		n += 1 + l + sovEvm(uint64(l))
	}
	l = m.TxV4Logs.Size()
	n += 1 + l + sovEvm(uint64(l))
	l = len(m.Ret)
	if l > 0 {
		n += 1 + l + sovEvm(uint64(l))
	}
	if m.Reverted {
		n += 2
	}
	if m.GasUsed != 0 {
		n += 1 + sovEvm(uint64(m.GasUsed))
	}
	return n
}

func (m *V4AccessTuple) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Address)
	if l > 0 {
		n += 1 + l + sovEvm(uint64(l))
	}
	if len(m.StorageKeys) > 0 {
		for _, s := range m.StorageKeys {
			l = len(s)
			n += 1 + l + sovEvm(uint64(l))
		}
	}
	return n
}

func (m *V4TraceConfig) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Tracer)
	if l > 0 {
		n += 1 + l + sovEvm(uint64(l))
	}
	l = len(m.Timeout)
	if l > 0 {
		n += 1 + l + sovEvm(uint64(l))
	}
	if m.Reexec != 0 {
		n += 1 + sovEvm(uint64(m.Reexec))
	}
	if m.DisableStack {
		n += 2
	}
	if m.DisableStorage {
		n += 2
	}
	if m.Debug {
		n += 2
	}
	if m.Limit != 0 {
		n += 1 + sovEvm(uint64(m.Limit))
	}
	if m.Overrides != nil {
		l = m.Overrides.Size()
		n += 1 + l + sovEvm(uint64(l))
	}
	if m.EnableMemory {
		n += 2
	}
	if m.EnableReturnData {
		n += 2
	}
	l = len(m.TracerJsonConfig)
	if l > 0 {
		n += 1 + l + sovEvm(uint64(l))
	}
	return n
}

func sovEvm(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozEvm(x uint64) (n int) {
	return sovEvm(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *V4Params) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEvm
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: V4Params: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: V4Params: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EvmDenom", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EvmDenom = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EnableCreate", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.EnableCreate = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EnableCall", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.EnableCall = bool(v != 0)
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExtraEIPs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ExtraEIPs.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field V4ChainConfig", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.V4ChainConfig.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllowUnprotectedTxs", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.AllowUnprotectedTxs = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipEvm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEvm
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExtraEIPs) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEvm
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ExtraEIPs: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ExtraEIPs: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowEvm
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.EIPs = append(m.EIPs, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowEvm
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthEvm
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthEvm
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.EIPs) == 0 {
					m.EIPs = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowEvm
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.EIPs = append(m.EIPs, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field EIPs", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipEvm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEvm
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *V4ChainConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEvm
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: V4ChainConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: V4ChainConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HomesteadBlock", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			var v github_com_cosmos_cosmos_sdk_types.Int
			m.HomesteadBlock = &v
			if err := m.HomesteadBlock.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DAOForkBlock", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			var v github_com_cosmos_cosmos_sdk_types.Int
			m.DAOForkBlock = &v
			if err := m.DAOForkBlock.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DAOForkSupport", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.DAOForkSupport = bool(v != 0)
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EIP150Block", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			var v github_com_cosmos_cosmos_sdk_types.Int
			m.EIP150Block = &v
			if err := m.EIP150Block.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EIP150Hash", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EIP150Hash = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EIP155Block", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			var v github_com_cosmos_cosmos_sdk_types.Int
			m.EIP155Block = &v
			if err := m.EIP155Block.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EIP158Block", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			var v github_com_cosmos_cosmos_sdk_types.Int
			m.EIP158Block = &v
			if err := m.EIP158Block.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ByzantiumBlock", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			var v github_com_cosmos_cosmos_sdk_types.Int
			m.ByzantiumBlock = &v
			if err := m.ByzantiumBlock.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConstantinopleBlock", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			var v github_com_cosmos_cosmos_sdk_types.Int
			m.ConstantinopleBlock = &v
			if err := m.ConstantinopleBlock.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PetersburgBlock", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			var v github_com_cosmos_cosmos_sdk_types.Int
			m.PetersburgBlock = &v
			if err := m.PetersburgBlock.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IstanbulBlock", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			var v github_com_cosmos_cosmos_sdk_types.Int
			m.IstanbulBlock = &v
			if err := m.IstanbulBlock.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MuirGlacierBlock", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			var v github_com_cosmos_cosmos_sdk_types.Int
			m.MuirGlacierBlock = &v
			if err := m.MuirGlacierBlock.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BerlinBlock", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			var v github_com_cosmos_cosmos_sdk_types.Int
			m.BerlinBlock = &v
			if err := m.BerlinBlock.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 17:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LondonBlock", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			var v github_com_cosmos_cosmos_sdk_types.Int
			m.LondonBlock = &v
			if err := m.LondonBlock.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 18:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ArrowGlacierBlock", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			var v github_com_cosmos_cosmos_sdk_types.Int
			m.ArrowGlacierBlock = &v
			if err := m.ArrowGlacierBlock.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 20:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GrayGlacierBlock", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			var v github_com_cosmos_cosmos_sdk_types.Int
			m.GrayGlacierBlock = &v
			if err := m.GrayGlacierBlock.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 21:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MergeNetsplitBlock", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			var v github_com_cosmos_cosmos_sdk_types.Int
			m.MergeNetsplitBlock = &v
			if err := m.MergeNetsplitBlock.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 22:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShanghaiBlock", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			var v github_com_cosmos_cosmos_sdk_types.Int
			m.ShanghaiBlock = &v
			if err := m.ShanghaiBlock.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 23:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CancunBlock", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			var v github_com_cosmos_cosmos_sdk_types.Int
			m.CancunBlock = &v
			if err := m.CancunBlock.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEvm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEvm
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *V4State) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEvm
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: V4State: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: V4State: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEvm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEvm
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransactionV4Logs) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEvm
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TransactionV4Logs: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TransactionV4Logs: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Hash", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Hash = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field V4Logs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.V4Logs = append(m.V4Logs, &V4Log{})
			if err := m.V4Logs[len(m.V4Logs)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEvm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEvm
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *V4Log) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEvm
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: V4Log: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: V4Log: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Address", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Address = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Topics", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Topics = append(m.Topics, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BlockNumber", wireType)
			}
			m.BlockNumber = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BlockNumber |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TxHash", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TxHash = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TxIndex", wireType)
			}
			m.TxIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TxIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BlockHash", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BlockHash = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Removed", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Removed = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipEvm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEvm
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *V4TxResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEvm
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: V4TxResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: V4TxResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ContractAddress", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ContractAddress = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Bloom", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Bloom = append(m.Bloom[:0], dAtA[iNdEx:postIndex]...)
			if m.Bloom == nil {
				m.Bloom = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TxV4Logs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.TxV4Logs.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ret", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ret = append(m.Ret[:0], dAtA[iNdEx:postIndex]...)
			if m.Ret == nil {
				m.Ret = []byte{}
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Reverted", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Reverted = bool(v != 0)
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field GasUsed", wireType)
			}
			m.GasUsed = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GasUsed |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipEvm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEvm
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *V4AccessTuple) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEvm
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: V4AccessTuple: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: V4AccessTuple: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Address", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Address = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StorageKeys", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StorageKeys = append(m.StorageKeys, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEvm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEvm
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *V4TraceConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEvm
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: V4TraceConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: V4TraceConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tracer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tracer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Timeout", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Timeout = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Reexec", wireType)
			}
			m.Reexec = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Reexec |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DisableStack", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.DisableStack = bool(v != 0)
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DisableStorage", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.DisableStorage = bool(v != 0)
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Debug", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Debug = bool(v != 0)
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Overrides", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Overrides == nil {
				m.Overrides = &V4ChainConfig{}
			}
			if err := m.Overrides.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EnableMemory", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.EnableMemory = bool(v != 0)
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EnableReturnData", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.EnableReturnData = bool(v != 0)
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TracerJsonConfig", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEvm
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEvm
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TracerJsonConfig = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEvm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEvm
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipEvm(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowEvm
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthEvm
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupEvm
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthEvm
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthEvm        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowEvm          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupEvm = fmt.Errorf("proto: unexpected end of group")
)