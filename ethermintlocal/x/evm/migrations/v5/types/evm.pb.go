// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: ethermint/evm/v1/evm.proto

package types

import (
	fmt "fmt"
	_ "github.com/cosmos/gogoproto/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package


// V5ExtraEIPs represents extra EIPs for the vm.Config
type V5ExtraEIPs struct {
	// eips defines the additional EIPs for the vm.Config
	EIPs []int64 `protobuf:"varint,1,rep,packed,name=eips,proto3" json:"eips,omitempty" yaml:"eips"`
}

func (m *V5ExtraEIPs) Reset()         { *m = V5ExtraEIPs{} }
func (m *V5ExtraEIPs) String() string { return proto.CompactTextString(m) }
func (*V5ExtraEIPs) ProtoMessage()    {}
func (*V5ExtraEIPs) Descriptor() ([]byte, []int) {
	return fileDescriptor_d21ecc92c8c8583e, []int{1}
}
func (m *V5ExtraEIPs) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *V5ExtraEIPs) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_V5ExtraEIPs.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *V5ExtraEIPs) XXX_Merge(src proto.Message) {
	xxx_messageInfo_V5ExtraEIPs.Merge(m, src)
}
func (m *V5ExtraEIPs) XXX_Size() int {
	return m.Size()
}
func (m *V5ExtraEIPs) XXX_DiscardUnknown() {
	xxx_messageInfo_V5ExtraEIPs.DiscardUnknown(m)
}

var xxx_messageInfo_V5ExtraEIPs proto.InternalMessageInfo

func (m *V5ExtraEIPs) GetEIPs() []int64 {
	if m != nil {
		return m.EIPs
	}
	return nil
}




func init() {
	proto.RegisterType((*V5ExtraEIPs)(nil), "ethermint.evm.v1.V5ExtraEIPs")
}

func init() { proto.RegisterFile("ethermint/evm/v1/evm.proto", fileDescriptor_d21ecc92c8c8583e) }

var fileDescriptor_d21ecc92c8c8583e = []byte{
	// 1644 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x58, 0x4f, 0x6f, 0xe3, 0xc6,
	0x15, 0xb7, 0x2d, 0xda, 0xa6, 0x46, 0xb2, 0x44, 0x8f, 0xb5, 0x5e, 0x65, 0x17, 0x35, 0x5d, 0x1e,
	0x02, 0x17, 0x4d, 0xec, 0xd8, 0x81, 0xd1, 0x45, 0x82, 0x16, 0x5d, 0xed, 0x3a, 0x89, 0xdd, 0x6d,
	0x6a, 0x8c, 0x1d, 0x14, 0x28, 0x50, 0x10, 0x23, 0x72, 0x42, 0x31, 0x26, 0x39, 0xc2, 0xcc, 0x50,
	0x2b, 0xb5, 0xfd, 0x00, 0x05, 0x7a, 0xe9, 0x27, 0x28, 0x72, 0xee, 0x27, 0x09, 0x7a, 0xda, 0x63,
	0xd1, 0x03, 0x51, 0x78, 0x6f, 0x3e, 0xea, 0x13, 0x14, 0xf3, 0x47, 0xd4, 0x1f, 0x1b, 0x6d, 0xad,
	0x93, 0xe7, 0xf7, 0xde, 0x9b, 0xdf, 0x6f, 0xde, 0x9b, 0x37, 0x9e, 0xa1, 0xc0, 0x33, 0x22, 0x7a,
	0x84, 0xa5, 0x71, 0x26, 0x8e, 0xc8, 0x20, 0x3d, 0x1a, 0x1c, 0xcb, 0x3f, 0x87, 0x7d, 0x46, 0x05,
	0x85, 0x4e, 0xe9, 0x3b, 0x94, 0xc6, 0xc1, 0xf1, 0xb3, 0x56, 0x44, 0x23, 0xaa, 0x9c, 0x47, 0x72,
	0xa4, 0xe3, 0xbc, 0xbf, 0x57, 0xc0, 0xc6, 0x25, 0x66, 0x38, 0xe5, 0xf0, 0x18, 0x54, 0xc9, 0x20,
	0xf5, 0x43, 0x92, 0xd1, 0xb4, 0xbd, 0xba, 0xbf, 0x7a, 0x50, 0xed, 0xb4, 0xc6, 0x85, 0xeb, 0x8c,
	0x70, 0x9a, 0x7c, 0xe6, 0x95, 0x2e, 0x0f, 0xd9, 0x64, 0x90, 0xbe, 0x96, 0x43, 0xf8, 0x73, 0xb0,
	0x45, 0x32, 0xdc, 0x4d, 0x88, 0x1f, 0x30, 0x82, 0x05, 0x69, 0xaf, 0xed, 0xaf, 0x1e, 0xd8, 0x9d,
	0xf6, 0xb8, 0x70, 0x5b, 0x66, 0xda, 0xac, 0xdb, 0x43, 0x75, 0x8d, 0x5f, 0x29, 0x08, 0x7f, 0x06,
	0x6a, 0x13, 0x3f, 0x4e, 0x92, 0x76, 0x45, 0x4d, 0xde, 0x1d, 0x17, 0x2e, 0x9c, 0x9f, 0x8c, 0x93,
	0xc4, 0x43, 0xc0, 0x4c, 0xc5, 0x49, 0x02, 0xbb, 0x00, 0x90, 0xa1, 0x60, 0xd8, 0x27, 0x71, 0x9f,
	0xb7, 0xad, 0xfd, 0xd5, 0x83, 0xda, 0xc9, 0xf3, 0xc3, 0xc5, 0x94, 0x0f, 0xcf, 0x64, 0xcc, 0xd9,
	0xf9, 0x25, 0xef, 0x7c, 0xf8, 0x43, 0xe1, 0xae, 0xdc, 0x16, 0x6e, 0xb5, 0x34, 0x8d, 0x0b, 0x77,
	0xdb, 0xa8, 0x94, 0x4c, 0x1e, 0xaa, 0x2a, 0x70, 0x16, 0xf7, 0x39, 0xfc, 0x3d, 0xa8, 0x07, 0x3d,
	0x1c, 0x67, 0x7e, 0x40, 0xb3, 0x6f, 0xe3, 0xa8, 0xbd, 0xae, 0x54, 0x7e, 0x74, 0x5f, 0xe5, 0x95,
	0x8c, 0x7a, 0xa5, 0x82, 0x3a, 0xcf, 0xa5, 0xce, 0xb8, 0x70, 0x77, 0x34, 0xf5, 0x2c, 0x81, 0x87,
	0x6a, 0xc1, 0x34, 0x12, 0x9e, 0x80, 0x27, 0x38, 0x49, 0xe8, 0x5b, 0x3f, 0xcf, 0xe4, 0x4e, 0x90,
	0x40, 0x90, 0xd0, 0x17, 0x43, 0xde, 0xde, 0x90, 0x55, 0x40, 0x3b, 0xca, 0xf9, 0xcd, 0xd4, 0x77,
	0x3d, 0xe4, 0xde, 0x0b, 0x30, 0x5d, 0x3f, 0xfc, 0x29, 0xb0, 0x54, 0xf6, 0xab, 0xfb, 0x95, 0x83,
	0x4a, 0xe7, 0xe9, 0x6d, 0xe1, 0x5a, 0x26, 0xaf, 0x9a, 0xc9, 0x4b, 0x65, 0xa4, 0x82, 0xbc, 0xbf,
	0x6d, 0x83, 0xda, 0xcc, 0x3a, 0x61, 0x0a, 0x9a, 0x3d, 0x9a, 0x12, 0x2e, 0x08, 0x0e, 0xfd, 0x6e,
	0x42, 0x83, 0x1b, 0xb3, 0xe3, 0xaf, 0xff, 0x55, 0xb8, 0x1f, 0x46, 0xb1, 0xe8, 0xe5, 0xdd, 0xc3,
	0x80, 0xa6, 0x47, 0x01, 0xe5, 0x29, 0xe5, 0xe6, 0xcf, 0xc7, 0x3c, 0xbc, 0x39, 0x12, 0xa3, 0x3e,
	0xe1, 0x87, 0xe7, 0x99, 0x18, 0x17, 0xee, 0xae, 0x56, 0x5a, 0xa0, 0xf2, 0x50, 0xa3, 0xb4, 0x74,
	0xa4, 0x01, 0x8e, 0x40, 0x23, 0xc4, 0xd4, 0xff, 0x96, 0xb2, 0x1b, 0xa3, 0xb6, 0xa6, 0xd4, 0xae,
	0xfe, 0x7f, 0xb5, 0xdb, 0xc2, 0xad, 0xbf, 0x7e, 0xf9, 0x9b, 0x2f, 0x28, 0xbb, 0x51, 0x9c, 0xe3,
	0xc2, 0x7d, 0xa2, 0xd5, 0xe7, 0x99, 0x3d, 0x54, 0x0f, 0x31, 0x2d, 0xc3, 0xe0, 0x6f, 0x81, 0x53,
	0x06, 0xf0, 0xbc, 0xdf, 0xa7, 0x4c, 0x98, 0x46, 0xfb, 0xf8, 0xb6, 0x70, 0x1b, 0x86, 0xf2, 0x4a,
	0x7b, 0xc6, 0x85, 0xfb, 0x74, 0x81, 0xd4, 0xcc, 0xf1, 0x50, 0xc3, 0xd0, 0x9a, 0x50, 0xc8, 0x41,
	0x9d, 0xc4, 0xfd, 0xe3, 0xd3, 0x4f, 0x4c, 0x46, 0x96, 0xca, 0xe8, 0xf2, 0x51, 0x19, 0xd5, 0xce,
	0xce, 0x2f, 0x8f, 0x4f, 0x3f, 0x99, 0x24, 0xb4, 0x53, 0x6e, 0x5c, 0x49, 0xeb, 0xa1, 0x9a, 0x86,
	0x3a, 0x9b, 0x73, 0x60, 0xa0, 0xdf, 0xc3, 0xbc, 0xa7, 0x7a, 0xb2, 0xda, 0x39, 0xb8, 0x2d, 0x5c,
	0xa0, 0x99, 0xbe, 0xc2, 0xbc, 0x37, 0xdd, 0x97, 0xee, 0xe8, 0x0f, 0x38, 0x13, 0x71, 0x9e, 0x4e,
	0xb8, 0x80, 0x9e, 0x2c, 0xa3, 0xca, 0xf5, 0x9f, 0x9a, 0xf5, 0x6f, 0x2c, 0xbd, 0xfe, 0xd3, 0x87,
	0xd6, 0x7f, 0x3a, 0xbf, 0x7e, 0x1d, 0x53, 0x8a, 0xbe, 0x30, 0xa2, 0x9b, 0x4b, 0x8b, 0xbe, 0x78,
	0x48, 0xf4, 0xc5, 0xbc, 0xa8, 0x8e, 0x91, 0xcd, 0xbe, 0x50, 0x89, 0xb6, 0xbd, 0x7c, 0xb3, 0xdf,
	0x2b, 0x6a, 0xa3, 0xb4, 0x68, 0xb9, 0x3f, 0x81, 0x56, 0x40, 0x33, 0x2e, 0xa4, 0x2d, 0xa3, 0xfd,
	0x84, 0x18, 0xcd, 0xaa, 0xd2, 0x3c, 0x7f, 0x94, 0xe6, 0x73, 0xf3, 0x7f, 0xe4, 0x01, 0x3e, 0x0f,
	0xed, 0xcc, 0x9b, 0xb5, 0x7a, 0x1f, 0x38, 0x7d, 0x22, 0x08, 0xe3, 0xdd, 0x9c, 0x45, 0x46, 0x19,
	0x28, 0xe5, 0xb3, 0x47, 0x29, 0x9b, 0x73, 0xb0, 0xc8, 0xe5, 0xa1, 0xe6, 0xd4, 0xa4, 0x15, 0xbf,
	0x03, 0x8d, 0x58, 0x2e, 0xa3, 0x9b, 0x27, 0x46, 0xaf, 0xa6, 0xf4, 0x5e, 0x3d, 0x4a, 0xcf, 0x1c,
	0xe6, 0x79, 0x26, 0x0f, 0x6d, 0x4d, 0x0c, 0x5a, 0x2b, 0x07, 0x30, 0xcd, 0x63, 0xe6, 0x47, 0x09,
	0x0e, 0x62, 0xc2, 0x8c, 0x5e, 0x5d, 0xe9, 0x7d, 0xf9, 0x28, 0xbd, 0x0f, 0xb4, 0xde, 0x7d, 0x36,
	0x0f, 0x39, 0xd2, 0xf8, 0xa5, 0xb6, 0x69, 0xd9, 0x10, 0xd4, 0xbb, 0x84, 0x25, 0x71, 0x66, 0x04,
	0xb7, 0x94, 0xe0, 0xcb, 0x47, 0x09, 0x9a, 0x3e, 0x9d, 0xe5, 0xf1, 0x50, 0x4d, 0xc3, 0x52, 0x25,
	0xa1, 0x59, 0x48, 0x27, 0x2a, 0xdb, 0xcb, 0xab, 0xcc, 0xf2, 0x78, 0xa8, 0xa6, 0xa1, 0x56, 0x19,
	0x82, 0x1d, 0xcc, 0x18, 0x7d, 0xbb, 0x50, 0x43, 0xa8, 0xc4, 0xbe, 0x7a, 0x94, 0xd8, 0x33, 0x2d,
	0xf6, 0x00, 0x9d, 0x87, 0xb6, 0x95, 0x75, 0xae, 0x8a, 0x39, 0x80, 0x11, 0xc3, 0xa3, 0x05, 0xe1,
	0xd6, 0xf2, 0x9b, 0x77, 0x9f, 0xcd, 0x43, 0x8e, 0x34, 0xce, 0xc9, 0xfe, 0x11, 0xb4, 0x52, 0xc2,
	0x22, 0xe2, 0x67, 0x44, 0xf0, 0x7e, 0x12, 0x0b, 0x23, 0xfc, 0x64, 0xf9, 0xf3, 0xf8, 0x10, 0x9f,
	0x87, 0xa0, 0x32, 0x7f, 0x6d, 0xac, 0xe5, 0xe1, 0xe0, 0x3d, 0x9c, 0x45, 0x3d, 0x1c, 0x1b, 0xd9,
	0xdd, 0xe5, 0x0f, 0xc7, 0x3c, 0x93, 0x87, 0xb6, 0x26, 0x86, 0xb2, 0x7f, 0x02, 0x9c, 0x05, 0xf9,
	0xa4, 0x7f, 0x9e, 0x2e, 0xdf, 0x3f, 0xb3, 0x3c, 0xf2, 0xe1, 0xa2, 0xa0, 0x52, 0xb9, 0xb0, 0xec,
	0x86, 0xd3, 0xbc, 0xb0, 0xec, 0xa6, 0xe3, 0x5c, 0x58, 0xb6, 0xe3, 0x6c, 0x5f, 0x58, 0xf6, 0x8e,
	0xd3, 0x42, 0x5b, 0x23, 0x9a, 0x50, 0x7f, 0xf0, 0xa9, 0x9e, 0x84, 0x6a, 0xe4, 0x2d, 0xe6, 0xe6,
	0x7f, 0x24, 0x6a, 0x04, 0x58, 0xe0, 0x64, 0xc4, 0x4d, 0xa9, 0x90, 0xa3, 0x0b, 0x38, 0x73, 0x6b,
	0x1f, 0x81, 0xf5, 0x2b, 0x21, 0xdf, 0x84, 0x0e, 0xa8, 0xdc, 0x90, 0x91, 0x7e, 0x8d, 0x20, 0x39,
	0x84, 0x2d, 0xb0, 0x3e, 0xc0, 0x49, 0xae, 0x1f, 0x97, 0x55, 0xa4, 0x81, 0x77, 0x09, 0x9a, 0xd7,
	0x0c, 0x67, 0x1c, 0x07, 0x22, 0xa6, 0xd9, 0x1b, 0x1a, 0x71, 0x08, 0x81, 0xa5, 0x6e, 0x45, 0x3d,
	0x57, 0x8d, 0xe1, 0x4f, 0x80, 0x95, 0xd0, 0x88, 0xb7, 0xd7, 0xf6, 0x2b, 0x07, 0xb5, 0x93, 0x27,
	0xf7, 0x5f, 0x6f, 0x6f, 0x68, 0x84, 0x54, 0x88, 0xf7, 0x8f, 0x35, 0x50, 0x79, 0x43, 0x23, 0xd8,
	0x06, 0x9b, 0x38, 0x0c, 0x19, 0xe1, 0xdc, 0x30, 0x4d, 0x20, 0xdc, 0x05, 0x1b, 0x82, 0xf6, 0xe3,
	0x40, 0xd3, 0x55, 0x91, 0x41, 0x52, 0x38, 0xc4, 0x02, 0xab, 0x77, 0x45, 0x1d, 0xa9, 0x31, 0x3c,
	0x01, 0x75, 0x95, 0x99, 0x9f, 0xe5, 0x69, 0x97, 0x30, 0xf5, 0x3c, 0xb0, 0x3a, 0xcd, 0xbb, 0xc2,
	0xad, 0x29, 0xfb, 0xd7, 0xca, 0x8c, 0x66, 0x01, 0xfc, 0x08, 0x6c, 0x8a, 0xe1, 0xec, 0xcd, 0xbe,
	0x73, 0x57, 0xb8, 0x4d, 0x31, 0x4d, 0x53, 0x5e, 0xdc, 0x68, 0x43, 0x0c, 0xd5, 0x05, 0x7e, 0x04,
	0x6c, 0x31, 0xf4, 0xe3, 0x2c, 0x24, 0x43, 0x75, 0x79, 0x5b, 0x9d, 0xd6, 0x5d, 0xe1, 0x3a, 0x33,
	0xe1, 0xe7, 0xd2, 0x87, 0x36, 0xc5, 0x50, 0x0d, 0xe0, 0x47, 0x00, 0xe8, 0x25, 0x29, 0x05, 0x7d,
	0xf5, 0x6e, 0xdd, 0x15, 0x6e, 0x55, 0x59, 0x15, 0xf7, 0x74, 0x08, 0x3d, 0xb0, 0xae, 0xb9, 0x6d,
	0xc5, 0x5d, 0xbf, 0x2b, 0x5c, 0x3b, 0xa1, 0x91, 0xe6, 0xd4, 0x2e, 0x59, 0x2a, 0x46, 0x52, 0x3a,
	0x20, 0xa1, 0xba, 0xdd, 0x6c, 0x34, 0x81, 0xde, 0x5f, 0xd6, 0x80, 0x7d, 0x3d, 0x44, 0x84, 0xe7,
	0x89, 0x80, 0x5f, 0x00, 0x27, 0xa0, 0x99, 0x60, 0x38, 0x10, 0xfe, 0x5c, 0x69, 0x3b, 0xcf, 0xa7,
	0x37, 0xcd, 0x62, 0x84, 0x87, 0x9a, 0x13, 0xd3, 0x4b, 0x53, 0xff, 0x16, 0x58, 0xef, 0x26, 0x94,
	0xa6, 0xaa, 0x13, 0xea, 0x48, 0x03, 0x88, 0x54, 0xd5, 0xd4, 0x2e, 0x57, 0xd4, 0x1b, 0xfd, 0xc7,
	0xf7, 0x77, 0x79, 0xa1, 0x55, 0x3a, 0xbb, 0xe6, 0x9d, 0xde, 0xd0, 0xda, 0x66, 0xbe, 0x27, 0x6b,
	0xab, 0x5a, 0xc9, 0x01, 0x15, 0x46, 0x84, 0xda, 0xb4, 0x3a, 0x92, 0x43, 0xf8, 0x0c, 0xd8, 0x8c,
	0x0c, 0x08, 0x13, 0x24, 0x54, 0x9b, 0x63, 0xa3, 0x12, 0xc3, 0x0f, 0x80, 0x1d, 0x61, 0xee, 0xe7,
	0x9c, 0x84, 0x7a, 0x27, 0xd0, 0x66, 0x84, 0xf9, 0x37, 0x9c, 0x84, 0x9f, 0x59, 0x7f, 0xfe, 0xde,
	0x5d, 0xf1, 0x30, 0xa8, 0xbd, 0x0c, 0x02, 0xc2, 0xf9, 0x75, 0xde, 0x4f, 0xc8, 0x7f, 0xe9, 0xb0,
	0x13, 0x50, 0xe7, 0x82, 0x32, 0x1c, 0x11, 0xff, 0x86, 0x8c, 0x4c, 0x9f, 0xe9, 0xae, 0x31, 0xf6,
	0x5f, 0x91, 0x11, 0x47, 0xb3, 0xc0, 0x48, 0x7c, 0x6f, 0x81, 0xda, 0x35, 0xc3, 0x01, 0x31, 0x2f,
	0x7c, 0xd9, 0xab, 0x12, 0x32, 0x23, 0x61, 0x90, 0xd4, 0x16, 0x71, 0x4a, 0x68, 0x2e, 0xcc, 0x79,
	0x9a, 0x40, 0x39, 0x83, 0x11, 0x32, 0x24, 0x81, 0x2a, 0xa3, 0x85, 0x0c, 0x82, 0xa7, 0x60, 0x2b,
	0x8c, 0xb9, 0xfa, 0x12, 0xe3, 0x02, 0x07, 0x37, 0x3a, 0xfd, 0x8e, 0x73, 0x57, 0xb8, 0x75, 0xe3,
	0xb8, 0x92, 0x76, 0x34, 0x87, 0xe0, 0xe7, 0xa0, 0x39, 0x9d, 0xa6, 0x56, 0xab, 0x3f, 0x6d, 0x3a,
	0xf0, 0xae, 0x70, 0x1b, 0x65, 0xa8, 0xf2, 0xa0, 0x05, 0x2c, 0x77, 0x3a, 0x24, 0xdd, 0x3c, 0x52,
	0xcd, 0x67, 0x23, 0x0d, 0xa4, 0x35, 0x89, 0xd3, 0x58, 0xa8, 0x66, 0x5b, 0x47, 0x1a, 0xc0, 0xcf,
	0x41, 0x95, 0x0e, 0x08, 0x63, 0x71, 0x48, 0xb8, 0x7a, 0xea, 0xfc, 0xaf, 0xaf, 0x34, 0x34, 0x8d,
	0x97, 0xc9, 0x99, 0xaf, 0xcc, 0x94, 0xa4, 0x94, 0x8d, 0xd4, 0xdb, 0xc5, 0x24, 0xa7, 0x1d, 0xbf,
	0x56, 0x76, 0x34, 0x87, 0x60, 0x07, 0x40, 0x33, 0x8d, 0x11, 0x91, 0xb3, 0xcc, 0x57, 0xe7, 0xbf,
	0xae, 0xe6, 0xaa, 0x53, 0xa8, 0xbd, 0x48, 0x39, 0x5f, 0x63, 0x81, 0xd1, 0x3d, 0x0b, 0xfc, 0x05,
	0x80, 0x7a, 0x4f, 0xfc, 0xef, 0x38, 0x2d, 0x3f, 0x33, 0xf5, 0xd3, 0x42, 0xe9, 0x6b, 0xaf, 0x59,
	0xb3, 0xa3, 0xd1, 0x05, 0xa7, 0x26, 0x8b, 0x0b, 0xcb, 0xb6, 0x9c, 0xf5, 0x0b, 0xcb, 0xde, 0x74,
	0xec, 0xb2, 0x7e, 0x26, 0x0b, 0xb4, 0x33, 0xc1, 0x33, 0xcb, 0xeb, 0xfc, 0xf2, 0x87, 0xdb, 0xbd,
	0xd5, 0x77, 0xb7, 0x7b, 0xab, 0xff, 0xbe, 0xdd, 0x5b, 0xfd, 0xeb, 0xfb, 0xbd, 0x95, 0x77, 0xef,
	0xf7, 0x56, 0xfe, 0xf9, 0x7e, 0x6f, 0xe5, 0x77, 0xb3, 0xf7, 0x03, 0x19, 0xc8, 0xeb, 0x61, 0xfa,
	0xd3, 0xc2, 0x50, 0xfd, 0xb8, 0xa0, 0xee, 0x88, 0xee, 0x86, 0xfa, 0xd1, 0xe0, 0xd3, 0xff, 0x04,
	0x00, 0x00, 0xff, 0xff, 0x68, 0xce, 0x8e, 0x23, 0x7a, 0x10, 0x00, 0x00,
}


func (m *V5ExtraEIPs) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *V5ExtraEIPs) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *V5ExtraEIPs) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.EIPs) > 0 {
		dAtA4 := make([]byte, len(m.EIPs)*10)
		var j3 int
		for _, num1 := range m.EIPs {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA4[j3] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j3++
			}
			dAtA4[j3] = uint8(num)
			j3++
		}
		i -= j3
		copy(dAtA[i:], dAtA4[:j3])
		i = encodeVarintEvm(dAtA, i, uint64(j3))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}


func encodeVarintEvm(dAtA []byte, offset int, v uint64) int {
	offset -= sovEvm(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}

func (m *V5ExtraEIPs) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.EIPs) > 0 {
		l = 0
		for _, e := range m.EIPs {
			l += sovEvm(uint64(e))
		}
		n += 1 + sovEvm(uint64(l)) + l
	}
	return n
}


func sovEvm(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozEvm(x uint64) (n int) {
	return sovEvm(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}

func (m *V5ExtraEIPs) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEvm
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: V5ExtraEIPs: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: V5ExtraEIPs: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowEvm
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.EIPs = append(m.EIPs, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowEvm
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthEvm
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthEvm
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.EIPs) == 0 {
					m.EIPs = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowEvm
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.EIPs = append(m.EIPs, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field EIPs", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipEvm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEvm
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}

func skipEvm(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowEvm
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowEvm
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthEvm
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupEvm
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthEvm
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthEvm        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowEvm          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupEvm = fmt.Errorf("proto: unexpected end of group")
)
