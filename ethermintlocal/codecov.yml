#
# This codecov.yml is the default configuration for
# all repositories on Codecov. You may adjust the settings
# below in your own codecov.yml in your repository.
#
coverage:
  precision: 2
  round: down
  range: 70...100

  status:
    # Learn more at https://docs.codecov.io/docs/commit-status
    project:
      default:
        threshold: 1% # allow this much decrease on project
      app:
        target: 70%
        flags:
          - app
      modules:
        target: 70%
        flags:
          - modules
      core:
        target: 50%
        flags:
          - core
      client:
        flags:
          - client
    changes: false

comment:
  layout: "reach, diff, files"
  behavior: default # update if exists else create new
  require_changes: true

flags:
  app:
    paths:
      - "app/"
  modules:
    paths:
      - "x/"
      - "!x/**/client/" # ignore client package
  core:
    paths:
      - "core/"
      - "crypto/"
      - "types/"
  clients:
    paths:
      - "rpc/"
      - "client/"
      - "x/**/client/"

ignore:
  - "docs"
  - "*.md"
  - "cmd"
  - "**/*.pb.go"
  - "**/*.pb.gw.go"
  - "types/*.pb.go"
  - "x/**/*.pb.gw.go"
  - "tests/*"
  - "scripts/"
