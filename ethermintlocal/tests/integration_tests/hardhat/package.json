{"name": "contracts", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"typechain": "npx hardhat typechain"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@nomiclabs/hardhat-waffle": "^2.0.3", "@openzeppelin/contracts": "^4.8.0", "@nomiclabs/hardhat-ethers": "^2.2.1", "@openzeppelin/hardhat-upgrades": "^1.21.0", "@openzeppelin/contracts-upgradeable": "^4.3.1", "@typechain/ethers-v5": "^5.0.0", "hardhat": "^2.10.1", "hardhat-typechain": "^0.3.5", "ts-generator": "^0.1.1", "typechain": "^4.0.3"}, "devDependencies": {"ts-node": "^10.9.1", "typescript": "^4.7.4"}}