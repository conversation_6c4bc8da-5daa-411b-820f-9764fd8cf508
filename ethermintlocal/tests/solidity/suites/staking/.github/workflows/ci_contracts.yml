name: contracts

on:
  push:
    branches: 
      - main
  pull_request:
    branches: 
      - '*'

jobs:
  CI:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2.4.0
      - name: Install node
        uses: actions/setup-node@v2.4.1
        with:
          node-version: 12
      - name: Install
        run: yarn
      - name: Lint
        run: yarn lint
      - name: Test
        run: yarn test
      - name: coverage
        continue-on-error: true
        run: yarn coverage
env:
  CI: true
