dotenv: .env
ethermint_9000-1:
  cmd: ethermintd
  start-flags: "--trace"
  app-config:
    minimum-gas-prices: 0aphoton
    index-events:
      - ethereum_tx.ethereumTxHash
    json-rpc:
      address: "127.0.0.1:{EVMRPC_PORT}"
      ws-address: "127.0.0.1:{EVMRPC_PORT_WS}"
      api: "eth,net,web3,debug"
  validators:
    - coins: ************0000000stake,************00000000000aphoton
      staked: ************0000000stake
      mnemonic: ${VALIDATOR1_MNEMONIC}
    - coins: ************0000000stake,************00000000000aphoton
      staked: ************0000000stake
      mnemonic: ${VALIDATOR2_MNEMONIC}
  accounts:
    - name: community
      coins: ************00000000000aphoton
      mnemonic: ${COMMUNITY_MNEMONIC}
    - name: signer1
      coins: 20000000000000000000000aphoton
      mnemonic: ${SIGNER1_MNEMONIC}
    - name: signer2
      coins: 30000000000000000000000aphoton
      mnemonic: ${SIGNER2_MNEMONIC}

  genesis:
    consensus_params:
      block:
        max_bytes: "1048576"
        max_gas: "********"
    app_state:
      evm:
        params:
          evm_denom: aphoton
      gov:
        voting_params:
          voting_period: "10s"
        deposit_params:
          max_deposit_period: "10s"
          min_deposit:
            - denom: "aphoton"
              amount: "1"
      transfer:
        params:
          receive_enabled: true
          send_enabled: true
      feemarket:
        params:
          no_base_fee: false
          base_fee: "************"
          min_gas_multiplier: "0"
