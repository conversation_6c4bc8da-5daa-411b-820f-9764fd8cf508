version: v1
plugins:
  - name: gocosmos
    out: .
    opt: plugins=grpc,Mgoogle/protobuf/duration.proto=Mgoogle/protobuf/duration.proto=github.com/gogo/protobuf/types,Mgoogle/protobuf/struct.proto=github.com/gogo/protobuf/types,Mgoogle/protobuf/timestamp.proto=github.com/gogo/protobuf/types,Mgoogle/protobuf/wrappers.proto=github.com/gogo/protobuf/types,Mgoogle/protobuf/any.proto=github.com/cosmos/cosmos-sdk/codec/types,Mcosmos/orm/v1alpha1/orm.proto=github.com/cosmos/cosmos-sdk/api/cosmos/orm/v1alpha1
  - name: grpc-gateway
    out: .
    opt: logtostderr=true
  - name: swagger
    out: ./tmp-swagger-gen
    opt:
      - logtostderr=true
      - fqn_for_swagger_name=true
      - simple_operation_ids=true
    strategy: all