syntax = "proto3";
package ethermint.evm.v1;

import "cosmos/msg/v1/msg.proto";
import "cosmos_proto/cosmos.proto";
import "ethermint/evm/v1/evm.proto";
import "gogoproto/gogo.proto";
import "google/api/annotations.proto";
import "google/protobuf/any.proto";

option go_package = "github.com/evmos/ethermint/x/evm/types";

// Msg defines the evm Msg service.
service Msg {
  // EthereumTx defines a method submitting Ethereum transactions.
  rpc EthereumTx(MsgEthereumTx) returns (MsgEthereumTxResponse) {
    option (google.api.http).post = "/ethermint/evm/v1/ethereum_tx";
  };
  // UpdateParams defined a governance operation for updating the x/evm module parameters.
  // The authority is hard-coded to the Cosmos SDK x/gov module account
  rpc UpdateParams(MsgUpdateParams) returns (MsgUpdateParamsResponse);
}

// MsgEthereumTx encapsulates an Ethereum transaction as an SDK message.
message MsgEthereumTx {
  option (gogoproto.goproto_getters) = false;

  // data is inner transaction data of the Ethereum transaction
  google.protobuf.Any data = 1;

  // size is the encoded storage size of the transaction (DEPRECATED)
  double size = 2 [(gogoproto.jsontag) = "-"];
  // hash of the transaction in hex format
  string hash = 3 [(gogoproto.moretags) = "rlp:\"-\""];
  // from is the ethereum signer address in hex format. This address value is checked
  // against the address derived from the signature (V, R, S) using the
  // secp256k1 elliptic curve
  string from = 4;
}

// LegacyTx is the transaction data of regular Ethereum transactions.
// NOTE: All non-protected transactions (i.e non EIP155 signed) will fail if the
// AllowUnprotectedTxs parameter is disabled.
message LegacyTx {
  option (gogoproto.goproto_getters) = false;
  option (cosmos_proto.implements_interface) = "TxData";

  // nonce corresponds to the account nonce (transaction sequence).
  uint64 nonce = 1;
  // gas_price defines the value for each gas unit
  string gas_price = 2 [(gogoproto.customtype) = "github.com/cosmos/cosmos-sdk/types.Int"];
  // gas defines the gas limit defined for the transaction.
  uint64 gas = 3 [(gogoproto.customname) = "GasLimit"];
  // to is the hex formatted address of the recipient
  string to = 4;
  // value defines the unsigned integer value of the transaction amount.
  string value = 5
      [(gogoproto.customtype) = "github.com/cosmos/cosmos-sdk/types.Int", (gogoproto.customname) = "Amount"];
  // data is the data payload bytes of the transaction.
  bytes data = 6;
  // v defines the signature value
  bytes v = 7;
  // r defines the signature value
  bytes r = 8;
  // s define the signature value
  bytes s = 9;
}

// AccessListTx is the data of EIP-2930 access list transactions.
message AccessListTx {
  option (gogoproto.goproto_getters) = false;
  option (cosmos_proto.implements_interface) = "TxData";

  // chain_id of the destination EVM chain
  string chain_id = 1 [
    (gogoproto.customtype) = "github.com/cosmos/cosmos-sdk/types.Int",
    (gogoproto.customname) = "ChainID",
    (gogoproto.jsontag) = "chainID"
  ];
  // nonce corresponds to the account nonce (transaction sequence).
  uint64 nonce = 2;
  // gas_price defines the value for each gas unit
  string gas_price = 3 [(gogoproto.customtype) = "github.com/cosmos/cosmos-sdk/types.Int"];
  // gas defines the gas limit defined for the transaction.
  uint64 gas = 4 [(gogoproto.customname) = "GasLimit"];
  // to is the recipient address in hex format
  string to = 5;
  // value defines the unsigned integer value of the transaction amount.
  string value = 6
      [(gogoproto.customtype) = "github.com/cosmos/cosmos-sdk/types.Int", (gogoproto.customname) = "Amount"];
  // data is the data payload bytes of the transaction.
  bytes data = 7;
  // accesses is an array of access tuples
  repeated AccessTuple accesses = 8
      [(gogoproto.castrepeated) = "AccessList", (gogoproto.jsontag) = "accessList", (gogoproto.nullable) = false];
  // v defines the signature value
  bytes v = 9;
  // r defines the signature value
  bytes r = 10;
  // s define the signature value
  bytes s = 11;
}

// DynamicFeeTx is the data of EIP-1559 dinamic fee transactions.
message DynamicFeeTx {
  option (gogoproto.goproto_getters) = false;
  option (cosmos_proto.implements_interface) = "TxData";

  // chain_id of the destination EVM chain
  string chain_id = 1 [
    (gogoproto.customtype) = "github.com/cosmos/cosmos-sdk/types.Int",
    (gogoproto.customname) = "ChainID",
    (gogoproto.jsontag) = "chainID"
  ];
  // nonce corresponds to the account nonce (transaction sequence).
  uint64 nonce = 2;
  // gas_tip_cap defines the max value for the gas tip
  string gas_tip_cap = 3 [(gogoproto.customtype) = "github.com/cosmos/cosmos-sdk/types.Int"];
  // gas_fee_cap defines the max value for the gas fee
  string gas_fee_cap = 4 [(gogoproto.customtype) = "github.com/cosmos/cosmos-sdk/types.Int"];
  // gas defines the gas limit defined for the transaction.
  uint64 gas = 5 [(gogoproto.customname) = "GasLimit"];
  // to is the hex formatted address of the recipient
  string to = 6;
  // value defines the the transaction amount.
  string value = 7
      [(gogoproto.customtype) = "github.com/cosmos/cosmos-sdk/types.Int", (gogoproto.customname) = "Amount"];
  // data is the data payload bytes of the transaction.
  bytes data = 8;
  // accesses is an array of access tuples
  repeated AccessTuple accesses = 9
      [(gogoproto.castrepeated) = "AccessList", (gogoproto.jsontag) = "accessList", (gogoproto.nullable) = false];
  // v defines the signature value
  bytes v = 10;
  // r defines the signature value
  bytes r = 11;
  // s define the signature value
  bytes s = 12;
}

// ExtensionOptionsEthereumTx is an extension option for ethereum transactions
message ExtensionOptionsEthereumTx {
  option (gogoproto.goproto_getters) = false;
}

// MsgEthereumTxResponse defines the Msg/EthereumTx response type.
message MsgEthereumTxResponse {
  option (gogoproto.goproto_getters) = false;

  // hash of the ethereum transaction in hex format. This hash differs from the
  // Tendermint sha256 hash of the transaction bytes. See
  // https://github.com/tendermint/tendermint/issues/6539 for reference
  string hash = 1;
  // logs contains the transaction hash and the proto-compatible ethereum
  // logs.
  repeated Log logs = 2;
  // ret is the returned data from evm function (result or data supplied with revert
  // opcode)
  bytes ret = 3;
  // vm_error is the error returned by vm execution
  string vm_error = 4;
  // gas_used specifies how much gas was consumed by the transaction
  uint64 gas_used = 5;
}

// MsgUpdateParams defines a Msg for updating the x/evm module parameters.
message MsgUpdateParams {
  option (cosmos.msg.v1.signer) = "authority";

  // authority is the address of the governance account.
  string authority = 1 [(cosmos_proto.scalar) = "cosmos.AddressString"];

  // params defines the x/evm parameters to update.
  // NOTE: All parameters must be supplied.
  Params params = 2 [(gogoproto.nullable) = false];
}

// MsgUpdateParamsResponse defines the response structure for executing a
// MsgUpdateParams message.
message MsgUpdateParamsResponse {}
