syntax = "proto3";
package ethermint.evm.v1;

import "cosmos/base/query/v1beta1/pagination.proto";
import "ethermint/evm/v1/evm.proto";
import "ethermint/evm/v1/tx.proto";
import "gogoproto/gogo.proto";
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/evmos/ethermint/x/evm/types";

// Query defines the gRPC querier service.
service Query {
  // Account queries an Ethereum account.
  rpc Account(QueryAccountRequest) returns (QueryAccountResponse) {
    option (google.api.http).get = "/ethermint/evm/v1/account/{address}";
  }

  // CosmosAccount queries an Ethereum account's Cosmos Address.
  rpc CosmosAccount(QueryCosmosAccountRequest) returns (QueryCosmosAccountResponse) {
    option (google.api.http).get = "/ethermint/evm/v1/cosmos_account/{address}";
  }

  // ValidatorAccount queries an Ethereum account's from a validator consensus
  // Address.
  rpc ValidatorAccount(QueryValidatorAccountRequest) returns (QueryValidatorAccountResponse) {
    option (google.api.http).get = "/ethermint/evm/v1/validator_account/{cons_address}";
  }

  // Balance queries the balance of a the EVM denomination for a single
  // EthAccount.
  rpc Balance(QueryBalanceRequest) returns (QueryBalanceResponse) {
    option (google.api.http).get = "/ethermint/evm/v1/balances/{address}";
  }

  // Storage queries the balance of all coins for a single account.
  rpc Storage(QueryStorageRequest) returns (QueryStorageResponse) {
    option (google.api.http).get = "/ethermint/evm/v1/storage/{address}/{key}";
  }

  // Code queries the balance of all coins for a single account.
  rpc Code(QueryCodeRequest) returns (QueryCodeResponse) {
    option (google.api.http).get = "/ethermint/evm/v1/codes/{address}";
  }

  // Params queries the parameters of x/evm module.
  rpc Params(QueryParamsRequest) returns (QueryParamsResponse) {
    option (google.api.http).get = "/ethermint/evm/v1/params";
  }

  // EthCall implements the `eth_call` rpc api
  rpc EthCall(EthCallRequest) returns (MsgEthereumTxResponse) {
    option (google.api.http).get = "/ethermint/evm/v1/eth_call";
  }

  // EstimateGas implements the `eth_estimateGas` rpc api
  rpc EstimateGas(EthCallRequest) returns (EstimateGasResponse) {
    option (google.api.http).get = "/ethermint/evm/v1/estimate_gas";
  }

  // TraceTx implements the `debug_traceTransaction` rpc api
  rpc TraceTx(QueryTraceTxRequest) returns (QueryTraceTxResponse) {
    option (google.api.http).get = "/ethermint/evm/v1/trace_tx";
  }

  // TraceBlock implements the `debug_traceBlockByNumber` and `debug_traceBlockByHash` rpc api
  rpc TraceBlock(QueryTraceBlockRequest) returns (QueryTraceBlockResponse) {
    option (google.api.http).get = "/ethermint/evm/v1/trace_block";
  }

  // BaseFee queries the base fee of the parent block of the current block,
  // it's similar to feemarket module's method, but also checks london hardfork status.
  rpc BaseFee(QueryBaseFeeRequest) returns (QueryBaseFeeResponse) {
    option (google.api.http).get = "/ethermint/evm/v1/base_fee";
  }
}

// QueryAccountRequest is the request type for the Query/Account RPC method.
message QueryAccountRequest {
  option (gogoproto.equal) = false;
  option (gogoproto.goproto_getters) = false;

  // address is the ethereum hex address to query the account for.
  string address = 1;
}

// QueryAccountResponse is the response type for the Query/Account RPC method.
message QueryAccountResponse {
  // balance is the balance of the EVM denomination.
  string balance = 1;
  // code_hash is the hex-formatted code bytes from the EOA.
  string code_hash = 2;
  // nonce is the account's sequence number.
  uint64 nonce = 3;
}

// QueryCosmosAccountRequest is the request type for the Query/CosmosAccount RPC
// method.
message QueryCosmosAccountRequest {
  option (gogoproto.equal) = false;
  option (gogoproto.goproto_getters) = false;

  // address is the ethereum hex address to query the account for.
  string address = 1;
}

// QueryCosmosAccountResponse is the response type for the Query/CosmosAccount
// RPC method.
message QueryCosmosAccountResponse {
  // cosmos_address is the cosmos address of the account.
  string cosmos_address = 1;
  // sequence is the account's sequence number.
  uint64 sequence = 2;
  // account_number is the account number
  uint64 account_number = 3;
}

// QueryValidatorAccountRequest is the request type for the
// Query/ValidatorAccount RPC method.
message QueryValidatorAccountRequest {
  option (gogoproto.equal) = false;
  option (gogoproto.goproto_getters) = false;

  // cons_address is the validator cons address to query the account for.
  string cons_address = 1;
}

// QueryValidatorAccountResponse is the response type for the
// Query/ValidatorAccount RPC method.
message QueryValidatorAccountResponse {
  // account_address is the cosmos address of the account in bech32 format.
  string account_address = 1;
  // sequence is the account's sequence number.
  uint64 sequence = 2;
  // account_number is the account number
  uint64 account_number = 3;
}

// QueryBalanceRequest is the request type for the Query/Balance RPC method.
message QueryBalanceRequest {
  option (gogoproto.equal) = false;
  option (gogoproto.goproto_getters) = false;

  // address is the ethereum hex address to query the balance for.
  string address = 1;
}

// QueryBalanceResponse is the response type for the Query/Balance RPC method.
message QueryBalanceResponse {
  // balance is the balance of the EVM denomination.
  string balance = 1;
}

// QueryStorageRequest is the request type for the Query/Storage RPC method.
message QueryStorageRequest {
  option (gogoproto.equal) = false;
  option (gogoproto.goproto_getters) = false;

  // address is the ethereum hex address to query the storage state for.
  string address = 1;

  // key defines the key of the storage state
  string key = 2;
}

// QueryStorageResponse is the response type for the Query/Storage RPC
// method.
message QueryStorageResponse {
  // value defines the storage state value hash associated with the given key.
  string value = 1;
}

// QueryCodeRequest is the request type for the Query/Code RPC method.
message QueryCodeRequest {
  option (gogoproto.equal) = false;
  option (gogoproto.goproto_getters) = false;

  // address is the ethereum hex address to query the code for.
  string address = 1;
}

// QueryCodeResponse is the response type for the Query/Code RPC
// method.
message QueryCodeResponse {
  // code represents the code bytes from an ethereum address.
  bytes code = 1;
}

// QueryTxLogsRequest is the request type for the Query/TxLogs RPC method.
message QueryTxLogsRequest {
  option (gogoproto.equal) = false;
  option (gogoproto.goproto_getters) = false;

  // hash is the ethereum transaction hex hash to query the logs for.
  string hash = 1;
  // pagination defines an optional pagination for the request.
  cosmos.base.query.v1beta1.PageRequest pagination = 2;
}

// QueryTxLogsResponse is the response type for the Query/TxLogs RPC method.
message QueryTxLogsResponse {
  // logs represents the ethereum logs generated from the given transaction.
  repeated Log logs = 1;
  // pagination defines the pagination in the response.
  cosmos.base.query.v1beta1.PageResponse pagination = 2;
}

// QueryParamsRequest defines the request type for querying x/evm parameters.
message QueryParamsRequest {}

// QueryParamsResponse defines the response type for querying x/evm parameters.
message QueryParamsResponse {
  // params define the evm module parameters.
  Params params = 1 [(gogoproto.nullable) = false];
}

// EthCallRequest defines EthCall request
message EthCallRequest {
  // args uses the same json format as the json rpc api.
  bytes args = 1;
  // gas_cap defines the default gas cap to be used
  uint64 gas_cap = 2;
  // proposer_address of the requested block in hex format
  bytes proposer_address = 3 [(gogoproto.casttype) = "github.com/cosmos/cosmos-sdk/types.ConsAddress"];
  // chain_id is the eip155 chain id parsed from the requested block header
  int64 chain_id = 4;
}

// EstimateGasResponse defines EstimateGas response
message EstimateGasResponse {
  // gas returns the estimated gas
  uint64 gas = 1;
}

// QueryTraceTxRequest defines TraceTx request
message QueryTraceTxRequest {
  // msg is the MsgEthereumTx for the requested transaction
  MsgEthereumTx msg = 1;
  // tx_index is not necessary anymore
  reserved 2;
  reserved "tx_index";
  // trace_config holds extra parameters to trace functions.
  TraceConfig trace_config = 3;
  // predecessors is an array of transactions included in the same block
  // need to be replayed first to get correct context for tracing.
  repeated MsgEthereumTx predecessors = 4;
  // block_number of requested transaction
  int64 block_number = 5;
  // block_hash of requested transaction
  string block_hash = 6;
  // block_time of requested transaction
  google.protobuf.Timestamp block_time = 7 [(gogoproto.nullable) = false, (gogoproto.stdtime) = true];
  // proposer_address is the proposer of the requested block
  bytes proposer_address = 8 [(gogoproto.casttype) = "github.com/cosmos/cosmos-sdk/types.ConsAddress"];
  // chain_id is the the eip155 chain id parsed from the requested block header
  int64 chain_id = 9;
}

// QueryTraceTxResponse defines TraceTx response
message QueryTraceTxResponse {
  // data is the response serialized in bytes
  bytes data = 1;
}

// QueryTraceBlockRequest defines TraceTx request
message QueryTraceBlockRequest {
  // txs is an array of messages in the block
  repeated MsgEthereumTx txs = 1;
  // trace_config holds extra parameters to trace functions.
  TraceConfig trace_config = 3;
  // block_number of the traced block
  int64 block_number = 5;
  // block_hash (hex) of the traced block
  string block_hash = 6;
  // block_time of the traced block
  google.protobuf.Timestamp block_time = 7 [(gogoproto.nullable) = false, (gogoproto.stdtime) = true];
  // proposer_address is the address of the requested block
  bytes proposer_address = 8 [(gogoproto.casttype) = "github.com/cosmos/cosmos-sdk/types.ConsAddress"];
  // chain_id is the eip155 chain id parsed from the requested block header
  int64 chain_id = 9;
}

// QueryTraceBlockResponse defines TraceBlock response
message QueryTraceBlockResponse {
  // data is the response serialized in bytes
  bytes data = 1;
}

// QueryBaseFeeRequest defines the request type for querying the EIP1559 base
// fee.
message QueryBaseFeeRequest {}

// QueryBaseFeeResponse returns the EIP1559 base fee.
message QueryBaseFeeResponse {
  // base_fee is the EIP1559 base fee
  string base_fee = 1 [(gogoproto.customtype) = "github.com/cosmos/cosmos-sdk/types.Int"];
}
