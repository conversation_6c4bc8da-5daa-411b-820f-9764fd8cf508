syntax = "proto3";
package ethermint.types.v1;

import "gogoproto/gogo.proto";

option go_package = "github.com/evmos/ethermint/types";

// TxResult is the value stored in eth tx indexer
message TxResult {
  option (gogoproto.goproto_getters) = false;

  // height of the blockchain
  int64 height = 1;
  // tx_index of the cosmos transaction
  uint32 tx_index = 2;
  // msg_index in a batch transaction
  uint32 msg_index = 3;

  // eth_tx_index is the index in the list of valid eth tx in the block,
  // aka. the transaction list returned by eth_getBlock api.
  int32 eth_tx_index = 4;
  // failed is true if the eth transaction did not go succeed
  bool failed = 5;
  // gas_used by the transaction. If it exceeds the block gas limit,
  // it's set to gas limit, which is what's actually deducted by ante handler.
  uint64 gas_used = 6;
  // cumulative_gas_used specifies the cumulated amount of gas used for all
  // processed messages within the current batch transaction.
  uint64 cumulative_gas_used = 7;
}
