syntax = "proto3";
package ethermint.types.v1;

import "gogoproto/gogo.proto";

option go_package = "github.com/evmos/ethermint/types";

// ExtensionOptionDynamicFeeTx is an extension option that specifies the maxPrioPrice for cosmos tx
message ExtensionOptionDynamicFeeTx {
  // max_priority_price is the same as `max_priority_fee_per_gas` in eip-1559 spec
  string max_priority_price = 1
      [(gogoproto.customtype) = "github.com/cosmos/cosmos-sdk/types.Int", (gogoproto.nullable) = false];
}
