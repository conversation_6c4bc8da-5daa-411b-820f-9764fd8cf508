syntax = "proto3";
package ethermint.types.v1;

import "cosmos/auth/v1beta1/auth.proto";
import "cosmos_proto/cosmos.proto";
import "gogoproto/gogo.proto";

option go_package = "github.com/evmos/ethermint/types";

// EthAccount implements the authtypes.AccountI interface and embeds an
// authtypes.BaseAccount type. It is compatible with the auth AccountKeeper.
message EthAccount {
  option (gogoproto.goproto_getters) = false;
  option (gogoproto.goproto_stringer) = false;
  option (gogoproto.equal) = false;

  option (cosmos_proto.implements_interface) = "github.com/cosmos/cosmos-sdk/x/auth/types.AccountI";

  // base_account is an authtypes.BaseAccount
  cosmos.auth.v1beta1.BaseAccount base_account = 1
      [(gogoproto.embed) = true, (gogoproto.moretags) = "yaml:\"base_account\""];

  // code_hash is the hash calculated from the code contents
  string code_hash = 2 [(gogoproto.moretags) = "yaml:\"code_hash\""];
}
