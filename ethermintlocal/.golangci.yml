run:
  tests: false
  # timeout for analysis, e.g. 30s, 5m, default is 1m
  # timeout: 5m

linters:
  enable:
    - bodyclose
    - depguard
    - dogsled
    - dupl
    - errcheck
    - goconst
    - gocritic
    - gofumpt
    - revive
    - gosec
    - gosimple
    - govet
    - ineffassign
    - lll
    - misspell
    - nakedret
    - prealloc
    - exportloopref
    - staticcheck
    - stylecheck
    - typecheck
    - unconvert
    - unparam
    - unused
    - nolintlint
    - asciicheck
    - exportloopref
    - gofumpt
    - gomodguard
    - whitespace

issues:
  exclude-rules:
    - path: _test\.go
      linters:
        - gosec
    - linters:
        - lll
      source: "https://"
  max-same-issues: 50

linters-settings:
  lll:
    line-length: 150
  dogsled:
    max-blank-identifiers: 3
  golint:
    min-confidence: 0
  maligned:
    suggest-new: true
  misspell:
    locale: US
  nolintlint:
    allow-unused: false
    allow-leading-space: true
    require-explanation: false
    require-specific: false
  gofumpt:
    lang-version: "1.19"
  gomodguard:
    blocked:
      versions: # List of blocked module version constraints
      - https://github.com/etcd-io/etcd:                            # Blocked module with version constraint
          version: ">= 3.4.10 || ~3.3.23"                           # Version constraint, see https://github.com/Masterminds/semver#basic-comparisons
          reason: "CVE-2020-15114; CVE-2020-15136; CVE-2020-15115"  # Reason why the version constraint exists. (Optional)
      - https://github.com/dgrijalva/jwt-go:                      # Blocked module with version constraint
          version: ">= 4.0.0-preview1"                           # Version constraint, see https://github.com/Masterminds/semver#basic-comparisons
          reason: "CVE-2020-26160"  # Reason why the version constraint exists. (Optional)
