# This workflow executes several linters on changed files based on languages used in your code base whenever
# you push a code or open a pull request.
#
# You can adjust the behavior by modifying this file.
# For more information, see:
# https://github.com/github/super-linter
---
name: Lint Code Base

on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]
jobs:
  run-lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          # Full git history is needed to get a proper list of changed files within `super-linter`
          fetch-depth: 0

      - name: Lint Code Base
        uses: github/super-linter@v5
        env:
          LINTER_RULES_PATH: /
          YAML_CONFIG_FILE: .yamllint
          VALIDATE_ALL_CODEBASE: false
          MARKDOWN_CONFIG_FILE: .markdownlint.yml
          PROTOBUF_CONFIG_FILE: .protolint.yml
          VALIDATE_NATURAL_LANGUAGE: false
          VALIDATE_OPENAPI: false
          VALIDATE_JSCPD: false
          VALIDATE_GO: false
          DEFAULT_BRANCH: "main"
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
