name: Check Markdown links
on: 
  pull_request:
    paths:
      - '**.md'
  push:
    branches:
      - main
    paths:
      - '**.md'

jobs:
  markdown-link-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: technote-space/get-diff-action@v6.1.2
        id: git_diff
        with:
          PATTERNS: |
            **/**.md
      - uses: gaurav-nelson/github-action-markdown-link-check@master
        with:
          folder-path: "docs"
          check-modified-files-only: "yes"
          use-quiet-mode: "yes"
          base-branch: "main"
          config-file: "mlc_config.json"
        if: env.GIT_DIFF
