name: Tests
on:
  pull_request:
  push:
    branches:
      - main
      - release/**

jobs:
  cleanup-runs:
    runs-on: ubuntu-latest
    steps:
      - uses: r<PERSON><PERSON><PERSON>/workflow-run-cleanup-action@master
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
    if: "!startsWith(github.ref, 'refs/tags/') && github.ref != 'refs/heads/main'"

  test-unit-cover:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/setup-go@v4
        with:
          go-version: 1.19
          check-latest: true
      - uses: actions/checkout@v3
      - uses: technote-space/get-diff-action@v6.1.2
        with:
          PATTERNS: |
            **/**.sol
            **/**.go
            go.mod
            go.sum
      - name: Test and Create Coverage Report
        run: |
          make test-unit-cover
        if: env.GIT_DIFF
      - uses: codecov/codecov-action@v3
        with:
          file: ./coverage.txt
          fail_ci_if_error: true
          token: ${{ secrets.CODECOV_TOKEN }}
        if: env.GIT_DIFF

  test-importer:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - uses: actions/setup-go@v4
        with:
          go-version: 1.19
          check-latest: true
      - uses: actions/checkout@v3
      - uses: technote-space/get-diff-action@v6.1.2
        id: git_diff
        with:
          PATTERNS: |
            **/**.go
            go.mod
            go.sum
      - name: test-importer
        run: |
          make test-import
        if: env.GIT_DIFF

  test-rpc:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/setup-go@v4
        with:
          go-version: 1.19
          check-latest: true
      - uses: actions/checkout@v3
      - uses: technote-space/get-diff-action@v6.1.2
        with:
          PATTERNS: |
            **/**.sol
            **/**.go
            go.mod
            go.sum
      - name: Test rpc endpoint
        run: |
          make test-rpc
        if: env.GIT_DIFF

  integration_tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: cachix/install-nix-action@v20
      - uses: cachix/cachix-action@v12
        with:
          name: ethermint
          signingKey: "${{ secrets.CACHIX_SIGNING_KEY }}"
      - uses: technote-space/get-diff-action@v6.1.2
        with:
          PATTERNS: |
            **/**.sol
            **/**.go
            go.mod
            go.sum
            tests/integration_tests/**
      - name: Run integration tests
        run: make run-integration-tests
        if: env.GIT_DIFF
      - name: 'Tar debug files'
        if: failure()
        run: tar cfz debug_files.tar.gz -C /tmp/pytest-of-runner .
      - uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: debug-files
          path: debug_files.tar.gz
          if-no-files-found: ignore

  upload-cache:
    if: github.event_name == 'push'
    needs: ["integration_tests"]
    strategy:
      matrix:
        os: [macos-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v3
      - uses: cachix/install-nix-action@v20
      - uses: cachix/cachix-action@v12
        with:
          name: ethermint
          signingKey: "${{ secrets.CACHIX_SIGNING_KEY }}"
      - name: 'instantiate integration test env'
        run: nix-store -r "$(nix-instantiate tests/integration_tests/shell.nix)"
