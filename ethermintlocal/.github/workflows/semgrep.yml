name: Semgrep
on:
  # <PERSON><PERSON> changed files in PRs, block on new issues only (existing issues ignored)
  pull_request: {}
  push:
    branches:
      - main
    paths:
      - .github/workflows/semgrep.yml
  schedule:
    - cron: '0 0 * * 0'
jobs:
  # Update from: https://semgrep.dev/docs/semgrep-ci/sample-ci-configs/#github-actions
  semgrep:
    name: <PERSON>an
    runs-on: ubuntu-latest
    container:
      image: returntocorp/semgrep
    if: (github.actor != 'dependabot[bot]')
    steps:
      - name: Permission issue fix
        run: git config --global --add safe.directory /__w/ethermint/ethermint
      - uses: actions/checkout@v3
      - name: Get Diff
        uses: technote-space/get-diff-action@v6.1.2
        with:
          PATTERNS: |
            **/*.go
            **/*.js
            **/*.ts
            **/*.sol
            go.mod
            go.sum
      - uses: actions/checkout@v3
      - run: semgrep scan --sarif --output=semgrep.sarif --config auto
        env:
          # Upload findings to GitHub Advanced Security Dashboard [step 1/2]
          SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}
        if: "env.GIT_DIFF_FILTERED != ''"
      # Upload findings to GitHub Advanced Security Dashboard [step 2/2]
      - name: Upload SARIF file
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: semgrep.sarif
        if: "env.GIT_DIFF_FILTERED != ''"
