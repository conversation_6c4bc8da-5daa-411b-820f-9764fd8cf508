name: goreleaser

on:
  push:
    tags:
      - "v*.*.*"
jobs:
  goreleaser:
    runs-on: ubuntu-latest
    environment: release
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: true
      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: 1.19
          check-latest: true
      - name: release dry run
        run: make release-dry-run
      - name: setup release environment
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |-
          echo 'GITHUB_TOKEN=${{secrets.GITHUB_TOKEN}}' > .release-env
      - name: release publish
        run: make release