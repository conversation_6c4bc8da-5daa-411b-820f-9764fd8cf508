name: Build
on:
  pull_request:
    branches:
      - main

jobs:
  cleanup-runs:
    runs-on: ubuntu-latest
    steps:
      - uses: r<PERSON><PERSON><PERSON>/workflow-run-cleanup-action@master
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
    if: "!startsWith(github.ref, 'refs/tags/') && github.ref != 'refs/heads/main'"

  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v4
        with:
          go-version: 1.19
          check-latest: true
      - uses: technote-space/get-diff-action@v6.1.2
        id: git_diff
        with:
          PATTERNS: |
            **/**.go
            go.mod
            go.sum
      - run: |
          make build
        if: env.GIT_DIFF
