version: 2
updates:
- package-ecosystem: github-actions
  directory: "/"
  schedule:
    interval: daily
    time: "10:00"
  open-pull-requests-limit: 10
  reviewers:
  - fedekunze
  - khoslaventures
  labels:
  - dependencies
- package-ecosystem: docker
  directory: "/"
  schedule:
    interval: daily
    time: "10:00"
  open-pull-requests-limit: 10
  reviewers:
  - fedekunze
  - khoslaventures
- package-ecosystem: gomod
  directory: "/"
  schedule:
    interval: daily
    time: "10:00"
  open-pull-requests-limit: 10
