schema = 3

[mod]
  [mod."cloud.google.com/go"]
    version = "v0.107.0"
    hash = "sha256-sb3SjuWj84ZUtIGYIioOmycfHLL0KZSYt/Y1zzfcd5M="
  [mod."cloud.google.com/go/compute"]
    version = "v1.15.1"
    hash = "sha256-F63wL70xdjxzFUKn8FEcBSI7FB94v1lo0f/aLDHiwMA="
  [mod."cloud.google.com/go/compute/metadata"]
    version = "v0.2.3"
    hash = "sha256-kYB1FTQRdTDqCqJzSU/jJYbVUGyxbkASUKbEs36FUyU="
  [mod."cloud.google.com/go/iam"]
    version = "v0.8.0"
    hash = "sha256-legngj53V4RLUbAzDFlzL7LYv5Ano8IW28Ia3Ar69BM="
  [mod."cloud.google.com/go/storage"]
    version = "v1.27.0"
    hash = "sha256-V4B6A1Ms8cemB5Cs6nAtUe1N1ldaI9oqTdzGU2FUhrc="
  [mod."cosmossdk.io/errors"]
    version = "v1.0.0-beta.7"
    hash = "sha256-XblGvIx6Wvvq6wggXjp+KbeJGXoe7AZH7hXEdauCezU="
  [mod."cosmossdk.io/math"]
    version = "v1.0.0-rc.0"
    hash = "sha256-bQ2hxg1dwVxbyBzbdepCz8nFGx6dfr63syqDNQ8AFQc="
  [mod."filippo.io/edwards25519"]
    version = "v1.0.0-rc.1"
    hash = "sha256-3DboBqby2ejRU33FG96Z8JF5AJ8HP2rC/v++VyoQ2LQ="
  [mod."github.com/99designs/keyring"]
    version = "v1.1.7-0.20210622111912-ef00f8ac3d76"
    hash = "sha256-oCalyOZWegRgKHZ1GvYHNkrMKh51j8cOE/K4yBPM5Oc="
    replaced = "github.com/cosmos/keyring"
  [mod."github.com/ChainSafe/go-schnorrkel"]
    version = "v0.0.0-20200405005733-88cbf1b4c40d"
    hash = "sha256-i8RXZemJGlSjBT35oPm0SawFiBoIU5Pkq5xp4n/rzCY="
  [mod."github.com/StackExchange/wmi"]
    version = "v0.0.0-20180116203802-5d049714c4a6"
    hash = "sha256-0yUxhZB3v3ZE3QY36zHs2cJ1S4GXptXIhyAi6sI2nOo="
  [mod."github.com/VictoriaMetrics/fastcache"]
    version = "v1.6.0"
    hash = "sha256-u1dkRJ2Y5+hnYlkyMPm14HxKkAv999bjN622nZDjaBo="
  [mod."github.com/Workiva/go-datastructures"]
    version = "v1.0.53"
    hash = "sha256-W6qOvqu8sokMlZrpOF1SWG138H0/BotywKNLlDF8Zug="
  [mod."github.com/armon/go-metrics"]
    version = "v0.4.1"
    hash = "sha256-usxTUHA0QQMdM6sHi2z51nmnEKMbA0qUilxJFpWHlYE="
  [mod."github.com/aws/aws-sdk-go"]
    version = "v1.44.122"
    hash = "sha256-bzkXf4Sf+6y6on7iulGtXkPdj0XoevkHYGQYPTNL7To="
  [mod."github.com/beorn7/perks"]
    version = "v1.0.1"
    hash = "sha256-h75GUqfwJKngCJQVE5Ao5wnO3cfKD9lSIteoLp/3xJ4="
  [mod."github.com/bgentry/go-netrc"]
    version = "v0.0.0-20140422174119-9fd32a8b3d3d"
    hash = "sha256-NDxQzO5C5M/aDz5/pjUHfZUh4VwIXovbb3irtxWCwjY="
  [mod."github.com/bgentry/speakeasy"]
    version = "v0.1.1-0.20220910012023-760eaf8b6816"
    hash = "sha256-Tx3sPuhsoVwrCfJdIwf4ipn7pD92OQNYvpCxl1Z9Wt0="
  [mod."github.com/btcsuite/btcd"]
    version = "v0.23.4"
    hash = "sha256-xP7TLBdOoUIjg5Q3MOjbT5P9tkCWjsd4bWgZLp539Wo="
  [mod."github.com/btcsuite/btcd/btcec/v2"]
    version = "v2.3.2"
    hash = "sha256-natWs+yIAuD1UI07iZtjPilroQLfXizFn3lNOiOT83U="
  [mod."github.com/btcsuite/btcd/btcutil"]
    version = "v1.1.3"
    hash = "sha256-6Y9sP1yvPBO8PhqmFVVXNV7dxsXlERDAB+TPTEfW3kI="
  [mod."github.com/btcsuite/btcd/chaincfg/chainhash"]
    version = "v1.0.1"
    hash = "sha256-vix0j/KGNvoKjhlKgVeSLY6un2FHeIEoZWMC4z3yvZ4="
  [mod."github.com/cenkalti/backoff/v4"]
    version = "v4.1.3"
    hash = "sha256-u6MEDopHoTWAZoVvvXOKnAg++xre53YgQx0gmf6t2KU="
  [mod."github.com/cespare/xxhash"]
    version = "v1.1.0"
    hash = "sha256-nVDTtXH9PC3yJ0THaQZEN243UP9xgLi/clt5xRqj3+M="
  [mod."github.com/cespare/xxhash/v2"]
    version = "v2.2.0"
    hash = "sha256-nPufwYQfTkyrEkbBrpqM3C2vnMxfIz6tAaBmiUP7vd4="
  [mod."github.com/chzyer/readline"]
    version = "v0.0.0-20180603132655-2972be24d48e"
    hash = "sha256-2Uj5LGpHEbLQG3d/7z9AL8DknUBZyoTAMs4j+VVDmIA="
  [mod."github.com/cockroachdb/apd/v2"]
    version = "v2.0.2"
    hash = "sha256-UrPHkvqVF8V78+kXKmjTHl79XsgDBnqFsje5BMYh0E4="
  [mod."github.com/coinbase/rosetta-sdk-go"]
    version = "v0.7.9"
    hash = "sha256-ZWIXIXcHGjeCNgMrpXymry8/8esDDauGFfF/+gEoO1Y="
  [mod."github.com/cometbft/cometbft-db"]
    version = "v0.7.0"
    hash = "sha256-EMvHp+5Hga5KkR9kkCiUBkz1HMfVhSHSxmpA5ZQ/Adw="
  [mod."github.com/confio/ics23/go"]
    version = "v0.9.0"
    hash = "sha256-guD8w7YygfUp7lpTAUyXQuCPx8F3lXkcg+yR5+JOCbk="
  [mod."github.com/cosmos/btcutil"]
    version = "v1.0.5"
    hash = "sha256-t572Sr5iiHcuMKLMWa2i+LBAt192oa+G1oA371tG/eI="
  [mod."github.com/cosmos/cosmos-proto"]
    version = "v1.0.0-beta.3"
    hash = "sha256-V0/ZhRdqK7Cqcv8X30gr33/hlI54bRXeHhI9LZKyLt8="
  [mod."github.com/cosmos/cosmos-sdk"]
    version = "v0.46.11"
    hash = "sha256-RpjzVDZw5n0y4kobBCy2d1oPmDCgL5k47UaZMI67sqU="
  [mod."github.com/cosmos/go-bip39"]
    version = "v1.0.0"
    hash = "sha256-Qm2aC2vaS8tjtMUbHmlBSagOSqbduEEDwc51qvQaBmA="
  [mod."github.com/cosmos/gogoproto"]
    version = "v1.4.7"
    hash = "sha256-JGSKV4CMgBGQYR7kZt6QQsVjgLEyAjNzKrtLalJqqVo="
  [mod."github.com/cosmos/gorocksdb"]
    version = "v1.2.0"
    hash = "sha256-209TcVuXc5s/TcOvNlaQ1HEJAUDTEK3nxPhs+d8TEcY="
  [mod."github.com/cosmos/iavl"]
    version = "v0.19.5"
    hash = "sha256-8PerCyxQrBCtr2zkhgriqauhCSoIe580dsYpZ44o+cE="
  [mod."github.com/cosmos/ibc-go/v6"]
    version = "v6.1.0"
    hash = "sha256-mHYCqLedcveDjfm1EiO2EMMNJqLm9u4WHwQTy1muOoc="
  [mod."github.com/cosmos/ledger-cosmos-go"]
    version = "v0.12.2"
    hash = "sha256-fLkveUWxn0nZzvgsY0KTU/T1TUUQ8Ap6XTYSnJs6XXo="
  [mod."github.com/creachadair/taskgroup"]
    version = "v0.3.2"
    hash = "sha256-Y261IO/d9xjV0UScqHvo31broxvnKn4IQQC9Mu6jNkE="
  [mod."github.com/danieljoos/wincred"]
    version = "v1.1.2"
    hash = "sha256-Nnklfg12vmWCOhELGyoRqEF4w4srp0WbPwreaChYLKs="
  [mod."github.com/davecgh/go-spew"]
    version = "v1.1.1"
    hash = "sha256-nhzSUrE1fCkN0+RL04N4h8jWmRFPPPWbCuDc7Ss0akI="
  [mod."github.com/deckarep/golang-set"]
    version = "v1.8.0"
    hash = "sha256-ELJKphksU9AOYwV3BtjvwPtUpbZvX9YMmo7eIiauQSc="
  [mod."github.com/decred/dcrd/dcrec/secp256k1/v4"]
    version = "v4.0.1"
    hash = "sha256-JBVRp40dDj0puapoQloV13H6TMjgJiypzYojcDRE2jI="
  [mod."github.com/desertbit/timer"]
    version = "v0.0.0-20180107155436-c41aec40b27f"
    hash = "sha256-abLOtEcomAqCWLphd2X6WkD/ED764w6sa6unox4BXss="
  [mod."github.com/dgraph-io/badger/v2"]
    version = "v2.2007.4"
    hash = "sha256-+KwqZJZpViv8S3TqUVvPXrFoMgWFyS3NoLsi4RR5fGk="
  [mod."github.com/dgraph-io/ristretto"]
    version = "v0.1.0"
    hash = "sha256-01jneg1+1x8tTfUTBZ+6mHkQaqXVnPYxLJyJhJQcvt4="
  [mod."github.com/dgryski/go-farm"]
    version = "v0.0.0-20200201041132-a6ae2369ad13"
    hash = "sha256-aOMlPwFY36bLiiIx4HonbCYRAhagk5N6HAWN7Ygif+E="
  [mod."github.com/dlclark/regexp2"]
    version = "v1.4.1-0.20201116162257-a2a8dda75c91"
    hash = "sha256-VNNMZIc7NkDg3DVLnqeJNM/KZqkkaZu2/HTLBL8X2xE="
  [mod."github.com/dop251/goja"]
    version = "v0.0.0-20220405120441-9037c2b61cbf"
    hash = "sha256-ueVIwPRUwxzd9+NybuFv1Pvp5tbwl9gGEgXB6cwqzEc="
  [mod."github.com/dustin/go-humanize"]
    version = "v1.0.0"
    hash = "sha256-gy4G1PnHD9iw2MitHX6y1y93qr3C9IncmXL7ttUMDs8="
  [mod."github.com/dvsekhvalnov/jose2go"]
    version = "v1.5.0"
    hash = "sha256-dsju6Xt83pe5SRPN/pUOnDUQByZ6hrhKIXWs3sSu7t8="
  [mod."github.com/edsrzf/mmap-go"]
    version = "v1.0.0"
    hash = "sha256-k1DYvCqO3BKNcGEve/nMW0RxzMkK2tGfXbUbycqcVSo="
  [mod."github.com/ethereum/go-ethereum"]
    version = "v1.10.26"
    hash = "sha256-gkMEwJ4rOgn12amD4QpZ4th/10uyTTeoFmpseuKDQPs="
  [mod."github.com/felixge/httpsnoop"]
    version = "v1.0.1"
    hash = "sha256-TNXnnC/ZGNY9lInAcES1cBGqIdEljKuh5LH/khVFjVk="
  [mod."github.com/fsnotify/fsnotify"]
    version = "v1.6.0"
    hash = "sha256-DQesOCweQPEwmAn6s7DCP/Dwy8IypC+osbpfsvpkdP0="
  [mod."github.com/gballet/go-libpcsclite"]
    version = "v0.0.0-20190607065134-2772fd86a8ff"
    hash = "sha256-Nr5ocU9s1F2Lhx/Zq6/nIo+KkKEqMjDYOEs3yWRC48g="
  [mod."github.com/go-kit/kit"]
    version = "v0.12.0"
    hash = "sha256-5RkXo6s0oye8etgD5qy+AvkkkNsQ6jc0kWJj6flA4GM="
  [mod."github.com/go-kit/log"]
    version = "v0.2.1"
    hash = "sha256-puLJ+up45X2j9E3lXvBPKqHPKOA/sFAhfCqGxsITW/Y="
  [mod."github.com/go-logfmt/logfmt"]
    version = "v0.5.1"
    hash = "sha256-t50m9ffvW8PiGvO+2svnLI+N/XaWaBS+ZlhwrEQn2gU="
  [mod."github.com/go-logr/logr"]
    version = "v1.2.3"
    hash = "sha256-2L7k6GfrcW3GXXYr1FYIu20aZBjIF0cTKdte6D4riH8="
  [mod."github.com/go-ole/go-ole"]
    version = "v1.2.6"
    hash = "sha256-+oxitLeJxYF19Z6g+6CgmCHJ1Y5D8raMi2Cb3M6nXCs="
  [mod."github.com/go-sourcemap/sourcemap"]
    version = "v2.1.3+incompatible"
    hash = "sha256-eXhXPPLnAy/rmt/zDgeqni2G3o58UtnHjR8vHLXvISI="
  [mod."github.com/go-stack/stack"]
    version = "v1.8.1"
    hash = "sha256-ixcJ2RrK1ZH3YWGQZF9QFBo02NOuLeSp9wJ7gniipgY="
  [mod."github.com/go-task/slim-sprig"]
    version = "v0.0.0-20230315185526-52ccab3ef572"
    hash = "sha256-D6NjCQbcYC53NdwzyAm4i9M1OjTJIVu4EIt3AD/Vxfg="
  [mod."github.com/godbus/dbus"]
    version = "v0.0.0-20190726142602-4481cbc300e2"
    hash = "sha256-R7Gb9+Zjy80FbQSDGketoVEqfdOQKuOVTfWRjQ5kxZY="
  [mod."github.com/gogo/gateway"]
    version = "v1.1.0"
    hash = "sha256-OHcA3fEGZt4uYn6V5BAaDc47DkH7z0Al+v7MpkfeR8o="
  [mod."github.com/gogo/protobuf"]
    version = "v1.3.3-alpha.regen.1"
    hash = "sha256-TKa//aFXpWH+yK/cN1oaaqhipZpPUovekP6oA9vLIHY="
    replaced = "github.com/regen-network/protobuf"
  [mod."github.com/golang/glog"]
    version = "v1.0.0"
    hash = "sha256-bglITqRgzi52zc6FoYYnfCvrjFWV4RVOacPCnbEBom4="
  [mod."github.com/golang/groupcache"]
    version = "v0.0.0-20210331224755-41bb18bfe9da"
    hash = "sha256-7Gs7CS9gEYZkbu5P4hqPGBpeGZWC64VDwraSKFF+VR0="
  [mod."github.com/golang/protobuf"]
    version = "v1.5.3"
    hash = "sha256-svogITcP4orUIsJFjMtp+Uv1+fKJv2Q5Zwf2dMqnpOQ="
  [mod."github.com/golang/snappy"]
    version = "v0.0.4"
    hash = "sha256-Umx+5xHAQCN/Gi4HbtMhnDCSPFAXSsjVbXd8n5LhjAA="
  [mod."github.com/google/btree"]
    version = "v1.1.2"
    hash = "sha256-K7V2obq3pLM71Mg0vhhHtZ+gtaubwXPQx3xcIyZDCjM="
  [mod."github.com/google/go-cmp"]
    version = "v0.5.9"
    hash = "sha256-lQc4O00R3QSMGs9LP8Sy7A9kj0cqV5rrUdpnGeipIyg="
  [mod."github.com/google/orderedcode"]
    version = "v0.0.1"
    hash = "sha256-KrExYovtUQrHGI1mPQf57jGw8soz7eWOC2xqEaV0uGk="
  [mod."github.com/google/pprof"]
    version = "v0.0.0-20210720184732-4bb14d4b1be1"
    hash = "sha256-m6l2Yay3iCu7Ses6nmwXifyztNqfP1B/MX81/tDK4Hw="
  [mod."github.com/google/uuid"]
    version = "v1.3.0"
    hash = "sha256-QoR55eBtA94T2tBszyxfDtO7/pjZZSGb5vm7U0Xhs0Y="
  [mod."github.com/googleapis/enterprise-certificate-proxy"]
    version = "v0.2.1"
    hash = "sha256-FN0dEdtJygo24AKwcHyy6pL+esTKfxSz9tXPX8FV1rE="
  [mod."github.com/googleapis/gax-go/v2"]
    version = "v2.7.0"
    hash = "sha256-n/9o8RFFDwowqSTLUPvsTE1lp4KUJpq53j733nLYrWk="
  [mod."github.com/gorilla/handlers"]
    version = "v1.5.1"
    hash = "sha256-GnBAARgOx1E+hDMQ63SI17hdhGtLQxb31lZOmn5j/pU="
  [mod."github.com/gorilla/mux"]
    version = "v1.8.0"
    hash = "sha256-s905hpzMH9bOLue09E2JmzPXfIS4HhAlgT7g13HCwKE="
  [mod."github.com/gorilla/websocket"]
    version = "v1.5.0"
    hash = "sha256-EYVgkSEMo4HaVrsWKqnsYRp8SSS8gNf7t+Elva02Ofc="
  [mod."github.com/grpc-ecosystem/go-grpc-middleware"]
    version = "v1.3.0"
    hash = "sha256-seaTQMNz/lWzpR3ex2gSM1Yo2yD2q6bJQZvB1L3CONk="
  [mod."github.com/grpc-ecosystem/grpc-gateway"]
    version = "v1.16.0"
    hash = "sha256-wLymGic7wZ6fSiBYDAaGqnQ9Ste1fUWeqXeolZXCHvI="
  [mod."github.com/gsterjov/go-libsecret"]
    version = "v0.0.0-20161001094733-a6f4afe4910c"
    hash = "sha256-Z5upjItPU9onq5t7VzhdQFp13lMJrSiE3gNRapuK6ic="
  [mod."github.com/gtank/merlin"]
    version = "v0.1.1"
    hash = "sha256-tfP9DFdPIfAt29pCta6dObAABCbZt4y3ZActH6ERkr0="
  [mod."github.com/gtank/ristretto255"]
    version = "v0.1.2"
    hash = "sha256-fAoVTP1s5+f7/YtnzI+gaEz1MS+FuCgy3sT19ZHIxE4="
  [mod."github.com/hashicorp/go-cleanhttp"]
    version = "v0.5.2"
    hash = "sha256-N9GOKYo7tK6XQUFhvhImtL7PZW/mr4C4Manx/yPVvcQ="
  [mod."github.com/hashicorp/go-getter"]
    version = "v1.7.0"
    hash = "sha256-kjXfacmxofi5mtyh9wpPeo3yBHNJMcTrfyEbYEAzLfs="
  [mod."github.com/hashicorp/go-immutable-radix"]
    version = "v1.3.1"
    hash = "sha256-65+A2HiVfS/GV9G+6/TkXXjzXhI/V98e6RlJWjxy+mg="
  [mod."github.com/hashicorp/go-safetemp"]
    version = "v1.0.0"
    hash = "sha256-g5i9m7FSRInQzZ4iRpIsoUu685AY7fppUwjhuZCezT8="
  [mod."github.com/hashicorp/go-version"]
    version = "v1.6.0"
    hash = "sha256-UV0equpmW6BiJnp4W3TZlSJ+PTHuTA+CdOs2JTeHhjs="
  [mod."github.com/hashicorp/golang-lru"]
    version = "v0.5.5-0.20210104140557-80c98217689d"
    hash = "sha256-w5utLMR7p5pF9xX+mI3N9NyfQ8ixNXNTgfXDu8fudmc="
  [mod."github.com/hashicorp/hcl"]
    version = "v1.0.0"
    hash = "sha256-xsRCmYyBfglMxeWUvTZqkaRLSW+V2FvNodEDjTGg1WA="
  [mod."github.com/hdevalence/ed25519consensus"]
    version = "v0.0.0-20220222234857-c00d1f31bab3"
    hash = "sha256-1ec2xc7l9oNtWJwVtx14HnozMZCe2DpfXmu1xI1Z/yo="
  [mod."github.com/holiman/bloomfilter/v2"]
    version = "v2.0.3"
    hash = "sha256-5VsJMQzJSNd4F7yAl3iF/q6JodWOlE4dUvTQ0UGPe+k="
  [mod."github.com/holiman/uint256"]
    version = "v1.2.2"
    hash = "sha256-mM0aeaqIwaNG7X3THx0HOCMPLKYK1DIvvuMLXClPAZg="
  [mod."github.com/huin/goupnp"]
    version = "v1.0.3"
    hash = "sha256-EMGmTdoQhP2bVbCPX37hes5krqXn6NFexfnKr9E5u8I="
  [mod."github.com/improbable-eng/grpc-web"]
    version = "v0.15.0"
    hash = "sha256-9oqKb5Y3hjleOFE2BczbEzLH6q2Jg7kUTP/M8Yk4Ne4="
  [mod."github.com/inconshreveable/mousetrap"]
    version = "v1.1.0"
    hash = "sha256-XWlYH0c8IcxAwQTnIi6WYqq44nOKUylSWxWO/vi+8pE="
  [mod."github.com/jackpal/go-nat-pmp"]
    version = "v1.0.2"
    hash = "sha256-L1D4Yoxnzihs795GZ+Q3AZsFP5c4iqyjTeyrudzPXtw="
  [mod."github.com/jmespath/go-jmespath"]
    version = "v0.4.0"
    hash = "sha256-xpT9g2qIXmPq7eeHUXHiDqJeQoHCudh44G/KCSFbcuo="
  [mod."github.com/jmhodges/levigo"]
    version = "v1.0.0"
    hash = "sha256-xEd0mDBeq3eR/GYeXjoTVb2sPs8sTCosn5ayWkcgENI="
  [mod."github.com/keybase/go-keychain"]
    version = "v0.0.0-20190712205309-48d3d31d256d"
    hash = "sha256-bn04wkDnhQ0tb/YzmPf7MNJlApOl+z6+EAbUqH7Ti5Q="
  [mod."github.com/klauspost/compress"]
    version = "v1.15.11"
    hash = "sha256-9MXm0TObg6DyqnYMIw3IChrorHc2ILf5djZYoM0e1J0="
  [mod."github.com/lib/pq"]
    version = "v1.10.6"
    hash = "sha256-8EhFwY/9YH5L/fd6l2beOnC3VvpegRAmCCsnDVJBqBM="
  [mod."github.com/libp2p/go-buffer-pool"]
    version = "v0.1.0"
    hash = "sha256-wQqGTtRWsfR9n0O/SXHVgECebbnNmHddxJIbG63OJBQ="
  [mod."github.com/magiconair/properties"]
    version = "v1.8.7"
    hash = "sha256-XQ2bnc2s7/IH3WxEO4GishZurMyKwEclZy1DXg+2xXc="
  [mod."github.com/manifoldco/promptui"]
    version = "v0.9.0"
    hash = "sha256-Fe2OPoyRExZejwtUBivKhfJAJW7o9b1eyYpgDlWQ1No="
  [mod."github.com/mattn/go-colorable"]
    version = "v0.1.13"
    hash = "sha256-qb3Qbo0CELGRIzvw7NVM1g/aayaz4Tguppk9MD2/OI8="
  [mod."github.com/mattn/go-isatty"]
    version = "v0.0.16"
    hash = "sha256-YMaPZvShDfA98vqw1+zWWl7M1IT4nHPGBrAt7kHo8Iw="
  [mod."github.com/mattn/go-runewidth"]
    version = "v0.0.9"
    hash = "sha256-dK/kIPe1tcxEubwI4CWfov/HWRBgD/fqlPC3d5i30CY="
  [mod."github.com/matttproud/golang_protobuf_extensions"]
    version = "v1.0.2-0.20181231171920-c182affec369"
    hash = "sha256-uovu7OycdeZ2oYQ7FhVxLey5ZX3T0FzShaRldndyGvc="
  [mod."github.com/mimoo/StrobeGo"]
    version = "v0.0.0-20210601165009-122bf33a46e0"
    hash = "sha256-rmw70RHsbeOnema++aFCPdswADMVKtb7KGF3msOI7ak="
  [mod."github.com/minio/highwayhash"]
    version = "v1.0.2"
    hash = "sha256-UeHeepKtToyA5e/w3KdmpbCn+4medesZG0cAcU6P2cY="
  [mod."github.com/mitchellh/go-homedir"]
    version = "v1.1.0"
    hash = "sha256-oduBKXHAQG8X6aqLEpqZHs5DOKe84u6WkBwi4W6cv3k="
  [mod."github.com/mitchellh/go-testing-interface"]
    version = "v1.14.1"
    hash = "sha256-TMGi38D13BEVN5cpeKDzKRIgLclm4ErOG+JEyqJrN/c="
  [mod."github.com/mitchellh/mapstructure"]
    version = "v1.5.0"
    hash = "sha256-ztVhGQXs67MF8UadVvG72G3ly0ypQW0IRDdOOkjYwoE="
  [mod."github.com/mtibben/percent"]
    version = "v0.2.1"
    hash = "sha256-Zj1lpCP6mKQ0UUTMs2By4LC414ou+iJzKkK+eBHfEcc="
  [mod."github.com/olekukonko/tablewriter"]
    version = "v0.0.5"
    hash = "sha256-/5i70IkH/qSW5KjGzv8aQNKh9tHoz98tqtL0K2DMFn4="
  [mod."github.com/onsi/ginkgo/v2"]
    version = "v2.9.2"
    hash = "sha256-+BCysb26cGinBvJkZZ9mW2BsbhpGvDXw4jJapcNuwaU="
  [mod."github.com/onsi/gomega"]
    version = "v1.27.6"
    hash = "sha256-nQ252v7WW3UMrx5e+toOpgm5u0qSUoWq4rDyTrDiOQk="
  [mod."github.com/pelletier/go-toml/v2"]
    version = "v2.0.6"
    hash = "sha256-BxAeApnn5H+OLlH3TXGvIbtC6LmbRnjwbcfT1qMZ4PE="
  [mod."github.com/petermattis/goid"]
    version = "v0.0.0-20180202154549-b0b1615b78e5"
    hash = "sha256-TCyVuP7rAtrvlterVCapFtbf6UmIf72FXQvkQoDtDj4="
  [mod."github.com/pkg/errors"]
    version = "v0.9.1"
    hash = "sha256-mNfQtcrQmu3sNg/7IwiieKWOgFQOVVe2yXgKBpe/wZw="
  [mod."github.com/pmezard/go-difflib"]
    version = "v1.0.0"
    hash = "sha256-/FtmHnaGjdvEIKAJtrUfEhV7EVo5A/eYrtdnUkuxLDA="
  [mod."github.com/prometheus/client_golang"]
    version = "v1.14.0"
    hash = "sha256-dpgGV8C30ZCn7b9mQ+Ye2AfPXTIuHLQbl2olMKzJKxA="
  [mod."github.com/prometheus/client_model"]
    version = "v0.3.0"
    hash = "sha256-vP+miJfsoK5UG9eug8z/bhAMj3bwg66T2vIh8WHoOKU="
  [mod."github.com/prometheus/common"]
    version = "v0.37.0"
    hash = "sha256-B2v0WsP8uKWYBpZcrog/sQXStIXwWZcVLmfPgnh1ZZA="
  [mod."github.com/prometheus/procfs"]
    version = "v0.8.0"
    hash = "sha256-hgrilokQsXCOCCvwgOSfuErxoFAQpXM/+zNJKcMVHyM="
  [mod."github.com/prometheus/tsdb"]
    version = "v0.7.1"
    hash = "sha256-BPz7YJbfMZgeR+u9YaeWeipVzHIS73EdgXD7VSJSLbA="
  [mod."github.com/rakyll/statik"]
    version = "v0.1.7"
    hash = "sha256-/bfnXHBmN8vviPL7D85IzcEVXCaWyjbPPNyauzEcQ8Q="
  [mod."github.com/rcrowley/go-metrics"]
    version = "v0.0.0-20201227073835-cf1acfcdf475"
    hash = "sha256-10ytHQ1SpMKYTiKuOPdEMuOVa8HVvv9ryYSIF9BHEBI="
  [mod."github.com/regen-network/cosmos-proto"]
    version = "v0.3.1"
    hash = "sha256-Bchbq/Hg72EA7Hevs8+PNuENuQaZAzk3qeVjMqFMUxc="
  [mod."github.com/rjeczalik/notify"]
    version = "v0.9.1"
    hash = "sha256-YLGNrHHM+mN4ElW/XWuylOnFrA/VjSY+eBuC4LN//5c="
  [mod."github.com/rs/cors"]
    version = "v1.8.3"
    hash = "sha256-VgVB4HKAhPSjNg96mIEUN1bt5ZQng8Fi3ZABy3CDWQE="
  [mod."github.com/rs/zerolog"]
    version = "v1.27.0"
    hash = "sha256-BxQtP2TROeSSpj9l1irocuSfxn55UL4ugzB/og7r8eE="
  [mod."github.com/sasha-s/go-deadlock"]
    version = "v0.3.1"
    hash = "sha256-2CBEi9/iN/OMt7wEIG+hRjgDH6CRWIgibGGGy1dQ78I="
  [mod."github.com/shirou/gopsutil"]
    version = "v3.21.4-0.20210419000835-c7a38de76ee5+incompatible"
    hash = "sha256-oqIqyFquWabIE6DID6uTEc8oFEmM1rVu2ATn3toiCEg="
  [mod."github.com/spf13/afero"]
    version = "v1.9.3"
    hash = "sha256-8WqLcfhb9IasbUWLbxD3g48t/cWc8XbgHUZOm3ALNjA="
  [mod."github.com/spf13/cast"]
    version = "v1.5.0"
    hash = "sha256-Pdp+wC5FWqyJKzyYHb7JCcV9BoJk/sxQw6nLyuLJvuQ="
  [mod."github.com/spf13/cobra"]
    version = "v1.7.0"
    hash = "sha256-bom9Zpnz8XPwx9IVF+GAodd3NVQ1dM1Uwxn8sy4Gmzs="
  [mod."github.com/spf13/jwalterweatherman"]
    version = "v1.1.0"
    hash = "sha256-62BQtqTLF/eVrTOr7pUXE7AiHRjOVC8jQs3/Ehmflfs="
  [mod."github.com/spf13/pflag"]
    version = "v1.0.5"
    hash = "sha256-w9LLYzxxP74WHT4ouBspH/iQZXjuAh2WQCHsuvyEjAw="
  [mod."github.com/spf13/viper"]
    version = "v1.15.0"
    hash = "sha256-FvpbekXegcdWNbek/vs2zakgRsT5FROF8O8fhn5DNpI="
  [mod."github.com/status-im/keycard-go"]
    version = "v0.2.0"
    hash = "sha256-UUiGmlgaIZDeMUJv3fdZBoQ9hJeSsg2ixRGmm6TgHug="
  [mod."github.com/stretchr/objx"]
    version = "v0.5.0"
    hash = "sha256-nY4mvP0f0Ry1IKMKQAYNuioA5h4red4mmQqeGZw6EF0="
  [mod."github.com/stretchr/testify"]
    version = "v1.8.2"
    hash = "sha256-n32PGyJL6VLtwOGEbS0lFchxunNU9nlol7OSEZlrKUM="
  [mod."github.com/subosito/gotenv"]
    version = "v1.4.2"
    hash = "sha256-LnrDR1k/AoCFWBMcU7vQsoQLkZ65evT2hoQHLDudTsg="
  [mod."github.com/syndtr/goleveldb"]
    version = "v1.0.1-0.20210819022825-2ae1ddf74ef7"
    hash = "sha256-36a4hgVQfwtS2zhylKpQuFhrjdc/Y8pF0dxc26jcZIU="
  [mod."github.com/tecbot/gorocksdb"]
    version = "v0.0.0-20191217155057-f0fad39f321c"
    hash = "sha256-VODgdnIxK9bhCHrt4nKfPYk/LLycTIwSRi930fws3Pk="
  [mod."github.com/tendermint/go-amino"]
    version = "v0.16.0"
    hash = "sha256-JW4zO/0vMzf1dXLePOqaMtiLUZgNbuIseh9GV+jQlf0="
  [mod."github.com/tendermint/tendermint"]
    version = "v0.34.27"
    hash = "sha256-BbpfLcLRf6PwB1xmgkhTm9ckZekelrDUlXDX0/FSMU8="
    replaced = "github.com/cometbft/cometbft"
  [mod."github.com/tendermint/tm-db"]
    version = "v0.6.7"
    hash = "sha256-hl/3RrBrpkk2zA6dmrNlIYKs1/GfqegSscDSkA5Pjlo="
  [mod."github.com/tidwall/btree"]
    version = "v1.5.0"
    hash = "sha256-iWll4/+ADLVse3VAHxXYLprILugX/+3u0ZIk0YlLv/Q="
  [mod."github.com/tidwall/gjson"]
    version = "v1.14.4"
    hash = "sha256-3DS2YNL95wG0qSajgRtIABD32J+oblaKVk8LIw+KSOc="
  [mod."github.com/tidwall/match"]
    version = "v1.1.1"
    hash = "sha256-M2klhPId3Q3T3VGkSbOkYl/2nLHnsG+yMbXkPkyrRdg="
  [mod."github.com/tidwall/pretty"]
    version = "v1.2.0"
    hash = "sha256-esRQGsn2Ee/CiySlwyuOICSLdqUkH4P7u8qXszos8Yc="
  [mod."github.com/tidwall/sjson"]
    version = "v1.2.5"
    hash = "sha256-OYGNolkmL7E1Qs2qrQ3IVpQp5gkcHNU/AB/z2O+Myps="
  [mod."github.com/tklauser/go-sysconf"]
    version = "v0.3.10"
    hash = "sha256-Zf2NsgM9+HeM949vCce4HQtSbfUiFpeiQ716yKcFyx4="
  [mod."github.com/tklauser/numcpus"]
    version = "v0.4.0"
    hash = "sha256-ndE82nOb3agubhEV7aRzEqqTlN4DPbKFHEm2+XZLn8k="
  [mod."github.com/tyler-smith/go-bip39"]
    version = "v1.1.0"
    hash = "sha256-3YhWBtSwRLGwm7vNwqumphZG3uLBW1vwT9QkQ8JuSjU="
  [mod."github.com/ulikunitz/xz"]
    version = "v0.5.10"
    hash = "sha256-bogOwQNmQVS7W+C7wci7XEUeYm9TB7PnxnyBIXKYbm0="
  [mod."github.com/zondax/hid"]
    version = "v0.9.1"
    hash = "sha256-hSVmN/f/lQHFhF60o6ej78ELC0MMoqQgqIX2hHjdTXg="
  [mod."github.com/zondax/ledger-go"]
    version = "v0.14.1"
    hash = "sha256-iQmShSaty50yYTbYPNd4fnOyrcEG7P2fWmj+fLJQW4s="
  [mod."go.etcd.io/bbolt"]
    version = "v1.3.6"
    hash = "sha256-DenVAmyN22xUiivk6fdJp4C9ZnUJXCMDUf8E0goRRV4="
  [mod."go.opencensus.io"]
    version = "v0.24.0"
    hash = "sha256-4H+mGZgG2c9I1y0m8avF4qmt8LUKxxVsTqR8mKgP4yo="
  [mod."golang.org/x/crypto"]
    version = "v0.5.0"
    hash = "sha256-5L4rCFZ0IMT9aQIeMbfOFbhwi03nXE/EeWuXup+Aeoc="
  [mod."golang.org/x/exp"]
    version = "v0.0.0-20230131160201-f062dba9d201"
    hash = "sha256-sxLT/VOe93v0h3miChJSHS9gscTZS/B71+390ju/e20="
  [mod."golang.org/x/net"]
    version = "v0.8.0"
    hash = "sha256-2cOtqa7aJ5mn64kZ+8+PVjJ4uGbhpXTpC1vm/+iaZzM="
  [mod."golang.org/x/oauth2"]
    version = "v0.4.0"
    hash = "sha256-Dj9wHbSbs0Ghr9Hef0hSfanaR8L0GShI18jGBT3yNn8="
  [mod."golang.org/x/sync"]
    version = "v0.1.0"
    hash = "sha256-Hygjq9euZ0qz6TvHYQwOZEjNiTbTh1nSLRAWZ6KFGR8="
  [mod."golang.org/x/sys"]
    version = "v0.6.0"
    hash = "sha256-zAgxiTuL24sGhbXrna9R1UYqLQh46ldztpumOScmduY="
  [mod."golang.org/x/term"]
    version = "v0.6.0"
    hash = "sha256-Ao0yXpwY8GyG+/23dVfJUYrfEfNUTES3RF45v1VhUAk="
  [mod."golang.org/x/text"]
    version = "v0.8.0"
    hash = "sha256-hgWFnT01DRmywBEXKYEVaOee7i6z8Ydz7zGbjcWwOgI="
  [mod."golang.org/x/tools"]
    version = "v0.7.0"
    hash = "sha256-ZEjfFulQd6U9r4mEJ5RZOnW49NZnQnrCFLMKCgLg7go="
  [mod."golang.org/x/xerrors"]
    version = "v0.0.0-20220907171357-04be3eba64a2"
    hash = "sha256-6+zueutgefIYmgXinOflz8qGDDDj0Zhv+2OkGhBTKno="
  [mod."google.golang.org/api"]
    version = "v0.107.0"
    hash = "sha256-jDYEv25KOU/k8fnS31DXl0a8lob0LmFxj88qV+P9NiU="
  [mod."google.golang.org/appengine"]
    version = "v1.6.7"
    hash = "sha256-zIxGRHiq4QBvRqkrhMGMGCaVL4iM4TtlYpAi/hrivS4="
  [mod."google.golang.org/genproto"]
    version = "v0.0.0-20230110181048-76db0878b65f"
    hash = "sha256-Jc90F9KU+ZKI0ynF/p3Vwl7TJPb7/MxDFs0ebagty2s="
  [mod."google.golang.org/grpc"]
    version = "v1.54.0"
    hash = "sha256-2HzpK4s9zAGUv/26wChxbfkX3t4WB1bLM96O5gkQmro="
  [mod."google.golang.org/protobuf"]
    version = "v1.29.1"
    hash = "sha256-ilSVvttGSP2xpqpoyQ0/Iuyx1WMiwe6GASKTfoeaqxw="
  [mod."gopkg.in/ini.v1"]
    version = "v1.67.0"
    hash = "sha256-V10ahGNGT+NLRdKUyRg1dos5RxLBXBk1xutcnquc/+4="
  [mod."gopkg.in/natefinch/npipe.v2"]
    version = "v2.0.0-20160621034901-c1b8fa8bdcce"
    hash = "sha256-ytqeVZqn4kd2uc65HvEjPlpPA2VnBmPfu5DsFlO0o+g="
  [mod."gopkg.in/yaml.v2"]
    version = "v2.4.0"
    hash = "sha256-uVEGglIedjOIGZzHW4YwN1VoRSTK8o0eGZqzd+TNdd0="
  [mod."gopkg.in/yaml.v3"]
    version = "v3.0.1"
    hash = "sha256-FqL9TKYJ0XkNwJFnq9j0VvJ5ZUU1RvH/52h/f5bkYAU="
  [mod."nhooyr.io/websocket"]
    version = "v1.8.6"
    hash = "sha256-DyaiCc/1iELrl6JSpz6WYMtFwUiSCOSoNF8IhSyP1ag="
  [mod."sigs.k8s.io/yaml"]
    version = "v1.3.0"
    hash = "sha256-RVp8vca2wxg8pcBDYospG7Z1dujoH7zXNu2rgZ1kky0="
