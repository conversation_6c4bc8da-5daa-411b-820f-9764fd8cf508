#!/bin/bash

# Build script for DATACOIN job-runner Docker images

set -e

echo "🚀 Building DATACOIN Job-Runner Docker Images..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to build a Docker image
build_image() {
    local job_type=$1
    local image_name="datacoin/${job_type}:latest"
    local dockerfile_path="./runners/${job_type}"
    
    echo -e "${YELLOW}🔨 Building ${image_name}...${NC}"
    
    if [ ! -d "$dockerfile_path" ]; then
        echo -e "${RED}❌ Directory $dockerfile_path not found, skipping...${NC}"
        return 1
    fi
    
    if [ ! -f "$dockerfile_path/Dockerfile" ]; then
        echo -e "${RED}❌ Dockerfile not found in $dockerfile_path, skipping...${NC}"
        return 1
    fi
    
    # Build the image
    if docker build -t "$image_name" "$dockerfile_path"; then
        echo -e "${GREEN}✅ Successfully built ${image_name}${NC}"
        return 0
    else
        echo -e "${RED}❌ Failed to build ${image_name}${NC}"
        return 1
    fi
}

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed or not in PATH${NC}"
    exit 1
fi

# Check if Docker daemon is running
if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Docker daemon is not running${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker is available${NC}"

# List of job types to build
JOB_TYPES=(
    "data-processing"
    "ai-training"
)

# Build each image
SUCCESSFUL_BUILDS=0
FAILED_BUILDS=0

for job_type in "${JOB_TYPES[@]}"; do
    if build_image "$job_type"; then
        ((SUCCESSFUL_BUILDS++))
    else
        ((FAILED_BUILDS++))
    fi
    echo ""
done

# Summary
echo "📊 Build Summary:"
echo -e "${GREEN}✅ Successful builds: $SUCCESSFUL_BUILDS${NC}"
echo -e "${RED}❌ Failed builds: $FAILED_BUILDS${NC}"

# List built images
echo ""
echo "🐳 Built Docker images:"
docker images | grep "datacoin/" | head -10

if [ $FAILED_BUILDS -eq 0 ]; then
    echo -e "${GREEN}🎉 All images built successfully!${NC}"
    exit 0
else
    echo -e "${YELLOW}⚠️ Some images failed to build. Check the logs above.${NC}"
    exit 1
fi
