version: '3.8'

services:
  # Job Orchestrator Service
  job-orchestrator:
    build:
      context: .
      dockerfile: Dockerfile.orchestrator
    container_name: datacoin-job-orchestrator
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./config:/app/config:ro
      - ./logs:/app/logs
    networks:
      - datacoin-network
    ports:
      - "3001:3001"
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for caching and job queue (optional)
  redis:
    image: redis:7-alpine
    container_name: datacoin-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    networks:
      - datacoin-network
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring service (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: datacoin-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - datacoin-network
    ports:
      - "9090:9090"

  # Log aggregation (optional)
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: datacoin-fluentd
    restart: unless-stopped
    volumes:
      - ./monitoring/fluentd.conf:/fluentd/etc/fluent.conf:ro
      - ./logs:/var/log/datacoin
    networks:
      - datacoin-network
    ports:
      - "24224:24224"
      - "24224:24224/udp"

networks:
  datacoin-network:
    driver: bridge
    name: datacoin-network

volumes:
  redis-data:
    driver: local
  prometheus-data:
    driver: local
