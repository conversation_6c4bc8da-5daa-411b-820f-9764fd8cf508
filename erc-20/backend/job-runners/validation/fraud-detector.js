class FraudDetector {
  constructor() {
    this.providerHistory = new Map(); // provider address -> execution history
    this.suspiciousProviders = new Set();
    this.benchmarkResults = new Map(); // jobType -> benchmark data
    
    // Fraud detection thresholds
    this.thresholds = {
      minExecutionTime: {
        'data_processing': 100,    // ms
        'text_analysis': 200,      // ms
        'image_analysis': 500,     // ms
        'ai_training': 5000,       // ms
        'text_generation': 1000,   // ms
        'image_generation': 3000,  // ms
        'speech_to_text': 1000     // ms
      },
      maxExecutionTime: {
        'data_processing': 30000,   // ms
        'text_analysis': 30000,     // ms
        'image_analysis': 60000,    // ms
        'ai_training': 300000,      // ms
        'text_generation': 120000,  // ms
        'image_generation': 180000, // ms
        'speech_to_text': 120000    // ms
      },
      maxConsecutiveFastResults: 5,
      maxConsecutiveSlowResults: 3,
      maxFailureRate: 0.2, // 20%
      minConfidenceScore: 0.7,
      maxDeviationFromBenchmark: 0.3 // 30%
    };
  }

  async analyzeExecution(providerAddress, jobId, jobType, params, result, executionTime) {
    console.log(`🔍 Analyzing execution for provider ${providerAddress}, job ${jobId}`);
    
    try {
      // Initialize provider history if not exists
      if (!this.providerHistory.has(providerAddress)) {
        this.providerHistory.set(providerAddress, {
          executions: [],
          stats: {
            totalJobs: 0,
            successfulJobs: 0,
            failedJobs: 0,
            avgExecutionTime: 0,
            consecutiveFastResults: 0,
            consecutiveSlowResults: 0,
            lastExecutionTime: Date.now()
          }
        });
      }

      const history = this.providerHistory.get(providerAddress);
      
      // Record execution
      const execution = {
        jobId,
        jobType,
        params,
        result,
        executionTime,
        timestamp: Date.now(),
        suspicious: false,
        suspiciousReasons: []
      };

      // Perform fraud checks
      const fraudAnalysis = await this.performFraudChecks(
        providerAddress, 
        execution, 
        history
      );

      execution.suspicious = fraudAnalysis.suspicious;
      execution.suspiciousReasons = fraudAnalysis.reasons;
      execution.fraudScore = fraudAnalysis.score;

      // Update history
      history.executions.push(execution);
      history.stats.totalJobs++;
      
      if (result.success) {
        history.stats.successfulJobs++;
      } else {
        history.stats.failedJobs++;
      }

      // Keep only last 100 executions
      if (history.executions.length > 100) {
        history.executions = history.executions.slice(-100);
      }

      // Update provider stats
      this.updateProviderStats(providerAddress, execution);

      // Check if provider should be flagged as suspicious
      if (fraudAnalysis.score > 0.7) {
        this.suspiciousProviders.add(providerAddress);
        console.log(`🚨 Provider ${providerAddress} flagged as suspicious (score: ${fraudAnalysis.score})`);
      }

      return {
        suspicious: execution.suspicious,
        fraudScore: execution.fraudScore,
        reasons: execution.suspiciousReasons,
        recommendation: this.getRecommendation(fraudAnalysis.score)
      };

    } catch (error) {
      console.error(`❌ Error analyzing execution:`, error.message);
      return {
        suspicious: false,
        fraudScore: 0,
        reasons: [],
        recommendation: 'ALLOW'
      };
    }
  }

  async performFraudChecks(providerAddress, execution, history) {
    const reasons = [];
    let score = 0;

    // 1. Execution time analysis
    const timeAnalysis = this.analyzeExecutionTime(execution, history);
    if (timeAnalysis.suspicious) {
      reasons.push(...timeAnalysis.reasons);
      score += timeAnalysis.score;
    }

    // 2. Result consistency analysis
    const consistencyAnalysis = this.analyzeResultConsistency(execution, history);
    if (consistencyAnalysis.suspicious) {
      reasons.push(...consistencyAnalysis.reasons);
      score += consistencyAnalysis.score;
    }

    // 3. Pattern analysis
    const patternAnalysis = this.analyzePatterns(execution, history);
    if (patternAnalysis.suspicious) {
      reasons.push(...patternAnalysis.reasons);
      score += patternAnalysis.score;
    }

    // 4. Benchmark comparison (single provider)
    const benchmarkAnalysis = await this.compareToBenchmark(execution);
    if (benchmarkAnalysis.suspicious) {
      reasons.push(...benchmarkAnalysis.reasons);
      score += benchmarkAnalysis.score;
    }

    // 5. Deterministic result validation (for applicable job types)
    const deterministicAnalysis = await this.validateDeterministicResult(execution);
    if (deterministicAnalysis.suspicious) {
      reasons.push(...deterministicAnalysis.reasons);
      score += deterministicAnalysis.score;
    }

    // 5. Statistical analysis
    const statsAnalysis = this.analyzeStatistics(execution, history);
    if (statsAnalysis.suspicious) {
      reasons.push(...statsAnalysis.reasons);
      score += statsAnalysis.score;
    }

    return {
      suspicious: score > 0.3,
      score: Math.min(score, 1.0),
      reasons: reasons
    };
  }

  async validateDeterministicResult(execution) {
    const reasons = [];
    let score = 0;

    // Only validate deterministic job types
    const deterministicJobs = ['data_processing'];
    if (!deterministicJobs.includes(execution.jobType)) {
      return { suspicious: false, score: 0, reasons: [] };
    }

    try {
      // For data processing, verify the calculation
      if (execution.jobType === 'data_processing') {
        const { params, result } = execution;
        const { numbers, operation = 'sum' } = params;

        if (Array.isArray(numbers) && numbers.length > 0) {
          let expectedResult;

          switch (operation) {
            case 'sum':
              expectedResult = numbers.reduce((a, b) => a + b, 0);
              break;
            case 'average':
              expectedResult = numbers.reduce((a, b) => a + b, 0) / numbers.length;
              break;
            case 'max':
              expectedResult = Math.max(...numbers);
              break;
            case 'min':
              expectedResult = Math.min(...numbers);
              break;
            default:
              // Cannot validate non-standard operations
              return { suspicious: false, score: 0, reasons: [] };
          }

          const tolerance = 0.0001;
          const actualResult = result.result;

          if (Math.abs(actualResult - expectedResult) > tolerance) {
            reasons.push(`Incorrect calculation: expected ${expectedResult}, got ${actualResult}`);
            score += 0.8; // High score for wrong calculation
          }
        }
      }
    } catch (error) {
      console.log(`⚠️ Error validating deterministic result: ${error.message}`);
    }

    return {
      suspicious: score > 0,
      score,
      reasons
    };
  }

  analyzeExecutionTime(execution, history) {
    const { jobType, executionTime } = execution;
    const reasons = [];
    let score = 0;

    const minTime = this.thresholds.minExecutionTime[jobType] || 100;
    const maxTime = this.thresholds.maxExecutionTime[jobType] || 30000;

    // Check if execution time is suspiciously fast
    if (executionTime < minTime) {
      reasons.push(`Execution time too fast: ${executionTime}ms < ${minTime}ms`);
      score += 0.4;
      
      // Track consecutive fast results
      history.stats.consecutiveFastResults++;
      if (history.stats.consecutiveFastResults > this.thresholds.maxConsecutiveFastResults) {
        reasons.push(`Too many consecutive fast results: ${history.stats.consecutiveFastResults}`);
        score += 0.3;
      }
    } else {
      history.stats.consecutiveFastResults = 0;
    }

    // Check if execution time is suspiciously slow
    if (executionTime > maxTime) {
      reasons.push(`Execution time too slow: ${executionTime}ms > ${maxTime}ms`);
      score += 0.2;
      
      history.stats.consecutiveSlowResults++;
      if (history.stats.consecutiveSlowResults > this.thresholds.maxConsecutiveSlowResults) {
        reasons.push(`Too many consecutive slow results: ${history.stats.consecutiveSlowResults}`);
        score += 0.2;
      }
    } else {
      history.stats.consecutiveSlowResults = 0;
    }

    return {
      suspicious: score > 0,
      score,
      reasons
    };
  }

  analyzeResultConsistency(execution, history) {
    const reasons = [];
    let score = 0;

    // Check confidence score if available
    if (execution.result && execution.result.confidence !== undefined) {
      if (execution.result.confidence < this.thresholds.minConfidenceScore * 100) {
        reasons.push(`Low confidence score: ${execution.result.confidence}`);
        score += 0.2;
      }
    }

    // Check for identical results (potential caching/cheating) - but only for non-deterministic jobs
    const nonDeterministicJobs = ['text_generation', 'image_generation', 'ai_training'];
    if (nonDeterministicJobs.includes(execution.jobType)) {
      const recentResults = history.executions
        .filter(e => e.jobType === execution.jobType)
        .slice(-10);

      const identicalResults = recentResults.filter(e =>
        JSON.stringify(e.result) === JSON.stringify(execution.result)
      ).length;

      if (identicalResults > 3) {
        reasons.push(`Too many identical results for non-deterministic job: ${identicalResults}`);
        score += 0.4;
      }
    }

    // For deterministic jobs, check if results are different when they should be same
    const deterministicJobs = ['data_processing', 'text_analysis', 'image_analysis'];
    if (deterministicJobs.includes(execution.jobType)) {
      const recentSameParams = history.executions
        .filter(e => e.jobType === execution.jobType)
        .filter(e => JSON.stringify(e.params) === JSON.stringify(execution.params))
        .slice(-5);

      if (recentSameParams.length > 0) {
        const differentResults = recentSameParams.filter(e =>
          JSON.stringify(e.result) !== JSON.stringify(execution.result)
        ).length;

        if (differentResults > 0) {
          reasons.push(`Inconsistent results for same deterministic input: ${differentResults} different results`);
          score += 0.5;
        }
      }
    }

    return {
      suspicious: score > 0,
      score,
      reasons
    };
  }

  analyzePatterns(execution, history) {
    const reasons = [];
    let score = 0;

    // Check execution frequency (potential bot behavior)
    const recentExecutions = history.executions.filter(e => 
      Date.now() - e.timestamp < 60000 // Last minute
    );

    if (recentExecutions.length > 10) {
      reasons.push(`High execution frequency: ${recentExecutions.length} jobs in last minute`);
      score += 0.2;
    }

    // Check for regular intervals (bot-like behavior)
    if (history.executions.length >= 5) {
      const intervals = [];
      for (let i = 1; i < Math.min(history.executions.length, 10); i++) {
        const interval = history.executions[i].timestamp - history.executions[i-1].timestamp;
        intervals.push(interval);
      }

      const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
      const variance = intervals.reduce((sum, interval) => 
        sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
      const stdDev = Math.sqrt(variance);

      // If intervals are too regular (low variance), it might be a bot
      if (stdDev < avgInterval * 0.1 && avgInterval < 10000) {
        reasons.push(`Regular execution intervals detected (possible bot)`);
        score += 0.3;
      }
    }

    return {
      suspicious: score > 0,
      score,
      reasons
    };
  }

  async compareToBenchmark(execution) {
    const reasons = [];
    let score = 0;

    const { jobType, result, executionTime } = execution;
    
    // Get or create benchmark for this job type
    if (!this.benchmarkResults.has(jobType)) {
      this.benchmarkResults.set(jobType, {
        avgExecutionTime: 0,
        avgConfidence: 0,
        sampleCount: 0,
        results: []
      });
    }

    const benchmark = this.benchmarkResults.get(jobType);

    if (benchmark.sampleCount > 0) {
      // Compare execution time to benchmark
      const timeDeviation = Math.abs(executionTime - benchmark.avgExecutionTime) / benchmark.avgExecutionTime;
      if (timeDeviation > this.thresholds.maxDeviationFromBenchmark) {
        reasons.push(`Execution time deviates from benchmark: ${(timeDeviation * 100).toFixed(1)}%`);
        score += 0.2;
      }

      // Compare confidence to benchmark (if available)
      if (result.confidence !== undefined && benchmark.avgConfidence > 0) {
        const confidenceDeviation = Math.abs(result.confidence - benchmark.avgConfidence) / benchmark.avgConfidence;
        if (confidenceDeviation > this.thresholds.maxDeviationFromBenchmark) {
          reasons.push(`Confidence deviates from benchmark: ${(confidenceDeviation * 100).toFixed(1)}%`);
          score += 0.1;
        }
      }
    }

    // Update benchmark (only with non-suspicious results)
    if (score === 0) {
      this.updateBenchmark(jobType, execution);
    }

    return {
      suspicious: score > 0,
      score,
      reasons
    };
  }

  analyzeStatistics(execution, history) {
    const reasons = [];
    let score = 0;

    if (history.stats.totalJobs >= 10) {
      // Check failure rate
      const failureRate = history.stats.failedJobs / history.stats.totalJobs;
      if (failureRate > this.thresholds.maxFailureRate) {
        reasons.push(`High failure rate: ${(failureRate * 100).toFixed(1)}%`);
        score += 0.2;
      }

      // Check for sudden changes in behavior
      const recentExecutions = history.executions.slice(-10);
      const recentAvgTime = recentExecutions.reduce((sum, e) => sum + e.executionTime, 0) / recentExecutions.length;
      const historicalAvgTime = history.stats.avgExecutionTime;

      if (historicalAvgTime > 0) {
        const timeChangeRatio = Math.abs(recentAvgTime - historicalAvgTime) / historicalAvgTime;
        if (timeChangeRatio > 0.5) {
          reasons.push(`Sudden change in execution time pattern: ${(timeChangeRatio * 100).toFixed(1)}%`);
          score += 0.2;
        }
      }
    }

    return {
      suspicious: score > 0,
      score,
      reasons
    };
  }

  updateProviderStats(providerAddress, execution) {
    const history = this.providerHistory.get(providerAddress);
    const stats = history.stats;

    // Update average execution time
    stats.avgExecutionTime = (
      (stats.avgExecutionTime * (stats.totalJobs - 1)) + execution.executionTime
    ) / stats.totalJobs;

    stats.lastExecutionTime = execution.timestamp;
  }

  updateBenchmark(jobType, execution) {
    const benchmark = this.benchmarkResults.get(jobType);
    
    // Update average execution time
    benchmark.avgExecutionTime = (
      (benchmark.avgExecutionTime * benchmark.sampleCount) + execution.executionTime
    ) / (benchmark.sampleCount + 1);

    // Update average confidence if available
    if (execution.result.confidence !== undefined) {
      benchmark.avgConfidence = (
        (benchmark.avgConfidence * benchmark.sampleCount) + execution.result.confidence
      ) / (benchmark.sampleCount + 1);
    }

    benchmark.sampleCount++;
    benchmark.results.push({
      executionTime: execution.executionTime,
      confidence: execution.result.confidence,
      timestamp: execution.timestamp
    });

    // Keep only last 1000 benchmark results
    if (benchmark.results.length > 1000) {
      benchmark.results = benchmark.results.slice(-1000);
    }
  }

  getRecommendation(fraudScore) {
    if (fraudScore < 0.3) return 'ALLOW';
    if (fraudScore < 0.7) return 'MONITOR';
    return 'BLOCK';
  }

  getProviderReport(providerAddress) {
    const history = this.providerHistory.get(providerAddress);
    if (!history) {
      return { error: 'Provider not found' };
    }

    const suspiciousExecutions = history.executions.filter(e => e.suspicious);
    const recentExecutions = history.executions.slice(-20);

    return {
      providerAddress,
      isSuspicious: this.suspiciousProviders.has(providerAddress),
      stats: history.stats,
      suspiciousExecutions: suspiciousExecutions.length,
      recentActivity: recentExecutions.map(e => ({
        jobId: e.jobId,
        jobType: e.jobType,
        executionTime: e.executionTime,
        suspicious: e.suspicious,
        fraudScore: e.fraudScore,
        timestamp: e.timestamp
      })),
      recommendation: this.getRecommendation(
        suspiciousExecutions.length / Math.max(history.stats.totalJobs, 1)
      )
    };
  }

  getAllSuspiciousProviders() {
    return Array.from(this.suspiciousProviders).map(address => 
      this.getProviderReport(address)
    );
  }

  clearProviderHistory(providerAddress) {
    this.providerHistory.delete(providerAddress);
    this.suspiciousProviders.delete(providerAddress);
  }

  getBenchmarkData() {
    const benchmarks = {};
    for (const [jobType, data] of this.benchmarkResults.entries()) {
      benchmarks[jobType] = {
        avgExecutionTime: Math.round(data.avgExecutionTime),
        avgConfidence: Math.round(data.avgConfidence * 100) / 100,
        sampleCount: data.sampleCount
      };
    }
    return benchmarks;
  }
}

module.exports = FraudDetector;
