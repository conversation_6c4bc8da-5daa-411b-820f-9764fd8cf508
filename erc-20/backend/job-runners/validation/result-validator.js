class ResultValidator {
  constructor() {
    this.validationRules = {
      'data_processing': {
        requiredFields: ['result', 'operation', 'processingTime'],
        resultType: 'object',
        maxSize: 1024
      },
      'text_analysis': {
        requiredFields: ['sentiment', 'confidence', 'processingTime'],
        resultType: 'object',
        maxSize: 512
      },
      'image_analysis': {
        requiredFields: ['confidence', 'processingTime'],
        resultType: 'object',
        maxSize: 1024
      }
    };
  }

  async validate(result, jobType, config) {
    console.log(`🔍 Validating result for job type: ${jobType}`);
    
    try {
      // Basic validation
      if (!result || typeof result !== 'object') {
        throw new Error('Result must be a valid object');
      }

      // Get validation rules
      const rules = this.validationRules[jobType] || config.validation;
      if (!rules) {
        console.log(`⚠️ No validation rules found for ${jobType}, using basic validation`);
        return this.basicValidation(result);
      }

      // Check required fields
      await this.validateRequiredFields(result, rules.requiredFields);

      // Check result size
      await this.validateSize(result, rules.maxSize);

      // Check result type
      await this.validateType(result, rules.resultType);

      // Job-specific validation
      await this.validateJobSpecific(result, jobType);

      console.log(`✅ Result validation passed for ${jobType}`);
      
      // Add validation metadata
      return {
        ...result,
        _validation: {
          validated: true,
          validatedAt: new Date().toISOString(),
          validator: 'datacoin-result-validator-v1.0'
        }
      };

    } catch (error) {
      console.error(`❌ Result validation failed for ${jobType}:`, error.message);
      throw new Error(`Result validation failed: ${error.message}`);
    }
  }

  async validateRequiredFields(result, requiredFields) {
    if (!requiredFields || !Array.isArray(requiredFields)) {
      return;
    }

    for (const field of requiredFields) {
      if (!(field in result)) {
        throw new Error(`Missing required field: ${field}`);
      }
      
      if (result[field] === null || result[field] === undefined) {
        throw new Error(`Required field '${field}' cannot be null or undefined`);
      }
    }
  }

  async validateSize(result, maxSize) {
    if (!maxSize) return;

    const resultString = JSON.stringify(result);
    const sizeInBytes = Buffer.byteLength(resultString, 'utf8');
    
    if (sizeInBytes > maxSize) {
      throw new Error(`Result size (${sizeInBytes} bytes) exceeds maximum allowed size (${maxSize} bytes)`);
    }
  }

  async validateType(result, expectedType) {
    if (!expectedType) return;

    const actualType = typeof result;
    if (actualType !== expectedType) {
      throw new Error(`Expected result type '${expectedType}', got '${actualType}'`);
    }
  }

  async validateJobSpecific(result, jobType) {
    switch (jobType) {
      case 'data_processing':
        await this.validateDataProcessing(result);
        break;
      case 'text_analysis':
        await this.validateTextAnalysis(result);
        break;
      case 'image_analysis':
        await this.validateImageAnalysis(result);
        break;
      default:
        // No specific validation
        break;
    }
  }

  async validateDataProcessing(result) {
    // Validate result is a number
    if (typeof result.result !== 'number' || isNaN(result.result)) {
      throw new Error('Data processing result must be a valid number');
    }

    // Validate operation
    const validOperations = ['sum', 'average', 'max', 'min', 'median', 'std'];
    if (!validOperations.includes(result.operation)) {
      throw new Error(`Invalid operation: ${result.operation}`);
    }

    // Validate processing time is reasonable
    if (typeof result.processingTime !== 'number' || result.processingTime < 0 || result.processingTime > 30000) {
      throw new Error('Invalid processing time');
    }
  }

  async validateTextAnalysis(result) {
    // Validate sentiment
    const validSentiments = ['positive', 'negative', 'neutral'];
    if (!validSentiments.includes(result.sentiment)) {
      throw new Error(`Invalid sentiment: ${result.sentiment}`);
    }

    // Validate confidence
    if (typeof result.confidence !== 'number' || result.confidence < 0 || result.confidence > 100) {
      throw new Error('Confidence must be a number between 0 and 100');
    }

    // Validate processing time
    if (typeof result.processingTime !== 'number' || result.processingTime < 0 || result.processingTime > 30000) {
      throw new Error('Invalid processing time');
    }
  }

  async validateImageAnalysis(result) {
    // Validate confidence
    if (typeof result.confidence !== 'number' || result.confidence < 0 || result.confidence > 100) {
      throw new Error('Confidence must be a number between 0 and 100');
    }

    // Validate processing time
    if (typeof result.processingTime !== 'number' || result.processingTime < 0 || result.processingTime > 60000) {
      throw new Error('Invalid processing time for image analysis');
    }

    // If it's basic analysis, validate dimensions
    if (result.width && result.height) {
      if (typeof result.width !== 'number' || typeof result.height !== 'number' ||
          result.width <= 0 || result.height <= 0) {
        throw new Error('Invalid image dimensions');
      }
    }
  }

  async basicValidation(result) {
    // Basic validation for unknown job types
    if (!result || typeof result !== 'object') {
      throw new Error('Result must be a valid object');
    }

    // Check for common fields
    if ('processingTime' in result) {
      if (typeof result.processingTime !== 'number' || result.processingTime < 0) {
        throw new Error('Invalid processing time');
      }
    }

    if ('confidence' in result) {
      if (typeof result.confidence !== 'number' || result.confidence < 0 || result.confidence > 100) {
        throw new Error('Invalid confidence value');
      }
    }

    return {
      ...result,
      _validation: {
        validated: true,
        validatedAt: new Date().toISOString(),
        validator: 'datacoin-basic-validator-v1.0'
      }
    };
  }

  // Single-provider validation for deterministic jobs
  async validateDeterministic(result, jobType, params) {
    console.log(`🔍 Validating deterministic result for ${jobType}`);

    try {
      switch (jobType) {
        case 'data_processing':
          return await this.validateDataProcessingResult(result, params);
        case 'text_analysis':
          return await this.validateTextAnalysisResult(result, params);
        default:
          return { valid: true, message: 'Deterministic validation not implemented for this job type' };
      }
    } catch (error) {
      return { valid: false, message: `Deterministic validation failed: ${error.message}` };
    }
  }

  async validateDataProcessingResult(result, params) {
    // For data processing, we can verify the calculation
    const { numbers, operation = 'sum' } = params;

    if (!Array.isArray(numbers)) {
      return { valid: false, message: 'Invalid input parameters' };
    }

    let expectedResult;
    switch (operation) {
      case 'sum':
        expectedResult = numbers.reduce((a, b) => a + b, 0);
        break;
      case 'average':
        expectedResult = numbers.reduce((a, b) => a + b, 0) / numbers.length;
        break;
      case 'max':
        expectedResult = Math.max(...numbers);
        break;
      case 'min':
        expectedResult = Math.min(...numbers);
        break;
      case 'median':
        const sorted = [...numbers].sort((a, b) => a - b);
        const mid = Math.floor(sorted.length / 2);
        expectedResult = sorted.length % 2 === 0
          ? (sorted[mid - 1] + sorted[mid]) / 2
          : sorted[mid];
        break;
      default:
        return { valid: true, message: 'Cannot validate non-deterministic operation' };
    }

    const tolerance = 0.0001; // Small tolerance for floating point
    const isValid = Math.abs(result.result - expectedResult) < tolerance;

    return {
      valid: isValid,
      message: isValid ? 'Result matches expected calculation' : `Expected ${expectedResult}, got ${result.result}`,
      expectedResult: expectedResult,
      actualResult: result.result
    };
  }

  async validateTextAnalysisResult(result, params) {
    // For text analysis, we can do basic sanity checks
    const { text } = params;

    if (!text || typeof text !== 'string') {
      return { valid: false, message: 'Invalid input text' };
    }

    // Basic sentiment validation based on obvious keywords
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'best'];
    const negativeWords = ['bad', 'terrible', 'awful', 'horrible', 'hate', 'worst', 'disgusting'];

    const textLower = text.toLowerCase();
    const positiveCount = positiveWords.filter(word => textLower.includes(word)).length;
    const negativeCount = negativeWords.filter(word => textLower.includes(word)).length;

    let expectedSentiment = 'neutral';
    if (positiveCount > negativeCount && positiveCount > 0) {
      expectedSentiment = 'positive';
    } else if (negativeCount > positiveCount && negativeCount > 0) {
      expectedSentiment = 'negative';
    }

    // Only validate if we have strong indicators
    if (positiveCount > 0 || negativeCount > 0) {
      const isValid = result.sentiment === expectedSentiment;
      return {
        valid: isValid,
        message: isValid ? 'Sentiment matches expected analysis' : `Expected ${expectedSentiment}, got ${result.sentiment}`,
        expectedSentiment: expectedSentiment,
        actualSentiment: result.sentiment,
        confidence: 'medium'
      };
    }

    return { valid: true, message: 'No strong sentiment indicators found, result accepted' };
  }
}

module.exports = ResultValidator;
