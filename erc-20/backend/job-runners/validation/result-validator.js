class ResultValidator {
  constructor() {
    this.validationRules = {
      'data_processing': {
        requiredFields: ['result', 'operation', 'processingTime'],
        resultType: 'object',
        maxSize: 1024
      },
      'text_analysis': {
        requiredFields: ['sentiment', 'confidence', 'processingTime'],
        resultType: 'object',
        maxSize: 512
      },
      'image_analysis': {
        requiredFields: ['confidence', 'processingTime'],
        resultType: 'object',
        maxSize: 1024
      }
    };
  }

  async validate(result, jobType, config) {
    console.log(`🔍 Validating result for job type: ${jobType}`);
    
    try {
      // Basic validation
      if (!result || typeof result !== 'object') {
        throw new Error('Result must be a valid object');
      }

      // Get validation rules
      const rules = this.validationRules[jobType] || config.validation;
      if (!rules) {
        console.log(`⚠️ No validation rules found for ${jobType}, using basic validation`);
        return this.basicValidation(result);
      }

      // Check required fields
      await this.validateRequiredFields(result, rules.requiredFields);

      // Check result size
      await this.validateSize(result, rules.maxSize);

      // Check result type
      await this.validateType(result, rules.resultType);

      // Job-specific validation
      await this.validateJobSpecific(result, jobType);

      console.log(`✅ Result validation passed for ${jobType}`);
      
      // Add validation metadata
      return {
        ...result,
        _validation: {
          validated: true,
          validatedAt: new Date().toISOString(),
          validator: 'datacoin-result-validator-v1.0'
        }
      };

    } catch (error) {
      console.error(`❌ Result validation failed for ${jobType}:`, error.message);
      throw new Error(`Result validation failed: ${error.message}`);
    }
  }

  async validateRequiredFields(result, requiredFields) {
    if (!requiredFields || !Array.isArray(requiredFields)) {
      return;
    }

    for (const field of requiredFields) {
      if (!(field in result)) {
        throw new Error(`Missing required field: ${field}`);
      }
      
      if (result[field] === null || result[field] === undefined) {
        throw new Error(`Required field '${field}' cannot be null or undefined`);
      }
    }
  }

  async validateSize(result, maxSize) {
    if (!maxSize) return;

    const resultString = JSON.stringify(result);
    const sizeInBytes = Buffer.byteLength(resultString, 'utf8');
    
    if (sizeInBytes > maxSize) {
      throw new Error(`Result size (${sizeInBytes} bytes) exceeds maximum allowed size (${maxSize} bytes)`);
    }
  }

  async validateType(result, expectedType) {
    if (!expectedType) return;

    const actualType = typeof result;
    if (actualType !== expectedType) {
      throw new Error(`Expected result type '${expectedType}', got '${actualType}'`);
    }
  }

  async validateJobSpecific(result, jobType) {
    switch (jobType) {
      case 'data_processing':
        await this.validateDataProcessing(result);
        break;
      case 'text_analysis':
        await this.validateTextAnalysis(result);
        break;
      case 'image_analysis':
        await this.validateImageAnalysis(result);
        break;
      default:
        // No specific validation
        break;
    }
  }

  async validateDataProcessing(result) {
    // Validate result is a number
    if (typeof result.result !== 'number' || isNaN(result.result)) {
      throw new Error('Data processing result must be a valid number');
    }

    // Validate operation
    const validOperations = ['sum', 'average', 'max', 'min', 'median', 'std'];
    if (!validOperations.includes(result.operation)) {
      throw new Error(`Invalid operation: ${result.operation}`);
    }

    // Validate processing time is reasonable
    if (typeof result.processingTime !== 'number' || result.processingTime < 0 || result.processingTime > 30000) {
      throw new Error('Invalid processing time');
    }
  }

  async validateTextAnalysis(result) {
    // Validate sentiment
    const validSentiments = ['positive', 'negative', 'neutral'];
    if (!validSentiments.includes(result.sentiment)) {
      throw new Error(`Invalid sentiment: ${result.sentiment}`);
    }

    // Validate confidence
    if (typeof result.confidence !== 'number' || result.confidence < 0 || result.confidence > 100) {
      throw new Error('Confidence must be a number between 0 and 100');
    }

    // Validate processing time
    if (typeof result.processingTime !== 'number' || result.processingTime < 0 || result.processingTime > 30000) {
      throw new Error('Invalid processing time');
    }
  }

  async validateImageAnalysis(result) {
    // Validate confidence
    if (typeof result.confidence !== 'number' || result.confidence < 0 || result.confidence > 100) {
      throw new Error('Confidence must be a number between 0 and 100');
    }

    // Validate processing time
    if (typeof result.processingTime !== 'number' || result.processingTime < 0 || result.processingTime > 60000) {
      throw new Error('Invalid processing time for image analysis');
    }

    // If it's basic analysis, validate dimensions
    if (result.width && result.height) {
      if (typeof result.width !== 'number' || typeof result.height !== 'number' ||
          result.width <= 0 || result.height <= 0) {
        throw new Error('Invalid image dimensions');
      }
    }
  }

  async basicValidation(result) {
    // Basic validation for unknown job types
    if (!result || typeof result !== 'object') {
      throw new Error('Result must be a valid object');
    }

    // Check for common fields
    if ('processingTime' in result) {
      if (typeof result.processingTime !== 'number' || result.processingTime < 0) {
        throw new Error('Invalid processing time');
      }
    }

    if ('confidence' in result) {
      if (typeof result.confidence !== 'number' || result.confidence < 0 || result.confidence > 100) {
        throw new Error('Invalid confidence value');
      }
    }

    return {
      ...result,
      _validation: {
        validated: true,
        validatedAt: new Date().toISOString(),
        validator: 'datacoin-basic-validator-v1.0'
      }
    };
  }

  // Cross-validation method for comparing results from multiple providers
  async crossValidate(results, jobType, tolerance = 0.1) {
    if (!Array.isArray(results) || results.length < 2) {
      return { valid: true, message: 'Insufficient results for cross-validation' };
    }

    console.log(`🔄 Cross-validating ${results.length} results for ${jobType}`);

    try {
      switch (jobType) {
        case 'data_processing':
          return await this.crossValidateDataProcessing(results, tolerance);
        case 'text_analysis':
          return await this.crossValidateTextAnalysis(results, tolerance);
        default:
          return { valid: true, message: 'Cross-validation not implemented for this job type' };
      }
    } catch (error) {
      return { valid: false, message: `Cross-validation failed: ${error.message}` };
    }
  }

  async crossValidateDataProcessing(results, tolerance) {
    const values = results.map(r => r.result);
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    
    // Check if all values are within tolerance of the mean
    const outliers = values.filter(value => Math.abs(value - mean) / mean > tolerance);
    
    if (outliers.length > 0) {
      return {
        valid: false,
        message: `Cross-validation failed: ${outliers.length} outliers detected`,
        outliers: outliers
      };
    }

    return { valid: true, message: 'Cross-validation passed' };
  }

  async crossValidateTextAnalysis(results, tolerance) {
    const sentiments = results.map(r => r.sentiment);
    const confidences = results.map(r => r.confidence);
    
    // Check sentiment consistency
    const uniqueSentiments = [...new Set(sentiments)];
    if (uniqueSentiments.length > 1) {
      return {
        valid: false,
        message: 'Cross-validation failed: Inconsistent sentiment results',
        sentiments: sentiments
      };
    }

    // Check confidence consistency
    const meanConfidence = confidences.reduce((a, b) => a + b, 0) / confidences.length;
    const outliers = confidences.filter(conf => Math.abs(conf - meanConfidence) / meanConfidence > tolerance);
    
    if (outliers.length > 0) {
      return {
        valid: false,
        message: 'Cross-validation failed: Inconsistent confidence values',
        outliers: outliers
      };
    }

    return { valid: true, message: 'Cross-validation passed' };
  }
}

module.exports = ResultValidator;
