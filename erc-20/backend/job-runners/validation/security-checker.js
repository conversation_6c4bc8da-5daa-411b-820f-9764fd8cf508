class SecurityChecker {
  constructor() {
    this.suspiciousPatterns = [
      // Command injection patterns
      /[;&|`$(){}[\]]/,
      /\b(eval|exec|system|shell_exec|passthru)\b/i,
      
      // Path traversal patterns
      /\.\.[\/\\]/,
      /[\/\\]etc[\/\\]/,
      /[\/\\]proc[\/\\]/,
      
      // Script injection patterns
      /<script[^>]*>/i,
      /javascript:/i,
      /on\w+\s*=/i,
      
      // SQL injection patterns
      /(\bUNION\b|\bSELECT\b|\bINSERT\b|\bDELETE\b|\bDROP\b)/i,
      
      // Network/URL patterns that might be suspicious
      /https?:\/\/(?![\w.-]+\.(jpg|jpeg|png|gif|bmp|webp|svg))/i
    ];

    this.maxStringLength = 10000;
    this.maxObjectDepth = 10;
    this.maxArrayLength = 1000;
  }

  async validateInput(params, jobType) {
    console.log(`🔒 Security check on input for job type: ${jobType}`);
    
    try {
      // Basic structure validation
      await this.validateStructure(params, 'input');
      
      // Content validation
      await this.validateContent(params, 'input');
      
      // Job-specific input validation
      await this.validateJobSpecificInput(params, jobType);
      
      console.log(`✅ Input security check passed for ${jobType}`);
      
    } catch (error) {
      console.error(`❌ Input security check failed for ${jobType}:`, error.message);
      throw new Error(`Security validation failed: ${error.message}`);
    }
  }

  async validateOutput(result, jobType) {
    console.log(`🔒 Security check on output for job type: ${jobType}`);
    
    try {
      // Basic structure validation
      await this.validateStructure(result, 'output');
      
      // Content validation
      await this.validateContent(result, 'output');
      
      // Check for data exfiltration attempts
      await this.checkDataExfiltration(result);
      
      console.log(`✅ Output security check passed for ${jobType}`);
      
    } catch (error) {
      console.error(`❌ Output security check failed for ${jobType}:`, error.message);
      throw new Error(`Security validation failed: ${error.message}`);
    }
  }

  async validateStructure(data, type) {
    // Check object depth
    const depth = this.getObjectDepth(data);
    if (depth > this.maxObjectDepth) {
      throw new Error(`${type} object depth (${depth}) exceeds maximum allowed (${this.maxObjectDepth})`);
    }

    // Check for circular references
    try {
      JSON.stringify(data);
    } catch (error) {
      if (error.message.includes('circular')) {
        throw new Error(`${type} contains circular references`);
      }
      throw error;
    }

    // Check overall size
    const jsonString = JSON.stringify(data);
    if (jsonString.length > this.maxStringLength * 10) {
      throw new Error(`${type} size too large`);
    }
  }

  async validateContent(data, type) {
    await this.scanForSuspiciousContent(data, type, []);
  }

  async scanForSuspiciousContent(obj, type, path) {
    if (typeof obj === 'string') {
      // Check string length
      if (obj.length > this.maxStringLength) {
        throw new Error(`String at ${path.join('.')} exceeds maximum length`);
      }

      // Check for suspicious patterns
      for (const pattern of this.suspiciousPatterns) {
        if (pattern.test(obj)) {
          throw new Error(`Suspicious pattern detected in ${type} at ${path.join('.')}: ${pattern}`);
        }
      }

      // Check for potential data exfiltration
      if (this.containsBase64Data(obj)) {
        console.log(`⚠️ Base64 data detected in ${type} at ${path.join('.')}`);
      }

    } else if (Array.isArray(obj)) {
      if (obj.length > this.maxArrayLength) {
        throw new Error(`Array at ${path.join('.')} exceeds maximum length (${this.maxArrayLength})`);
      }

      for (let i = 0; i < obj.length; i++) {
        await this.scanForSuspiciousContent(obj[i], type, [...path, `[${i}]`]);
      }

    } else if (obj && typeof obj === 'object') {
      for (const [key, value] of Object.entries(obj)) {
        // Check key for suspicious content
        if (typeof key === 'string') {
          for (const pattern of this.suspiciousPatterns) {
            if (pattern.test(key)) {
              throw new Error(`Suspicious pattern in object key: ${key}`);
            }
          }
        }

        await this.scanForSuspiciousContent(value, type, [...path, key]);
      }
    }
  }

  async validateJobSpecificInput(params, jobType) {
    switch (jobType) {
      case 'data_processing':
        await this.validateDataProcessingInput(params);
        break;
      case 'text_analysis':
        await this.validateTextAnalysisInput(params);
        break;
      case 'image_analysis':
        await this.validateImageAnalysisInput(params);
        break;
      default:
        // No specific validation
        break;
    }
  }

  async validateDataProcessingInput(params) {
    if (!params.numbers || !Array.isArray(params.numbers)) {
      throw new Error('Data processing requires numbers array');
    }

    if (params.numbers.length > 10000) {
      throw new Error('Numbers array too large (max 10000 elements)');
    }

    // Check all elements are valid numbers
    for (let i = 0; i < params.numbers.length; i++) {
      const num = params.numbers[i];
      if (typeof num !== 'number' || !isFinite(num)) {
        throw new Error(`Invalid number at index ${i}: ${num}`);
      }
      
      // Check for extremely large numbers that might cause issues
      if (Math.abs(num) > Number.MAX_SAFE_INTEGER) {
        throw new Error(`Number too large at index ${i}: ${num}`);
      }
    }
  }

  async validateTextAnalysisInput(params) {
    if (!params.text || typeof params.text !== 'string') {
      throw new Error('Text analysis requires text string');
    }

    if (params.text.length > 10000) {
      throw new Error('Text too long (max 10000 characters)');
    }

    // Check for potential script injection in text
    const scriptPatterns = [
      /<script[^>]*>/i,
      /javascript:/i,
      /on\w+\s*=/i
    ];

    for (const pattern of scriptPatterns) {
      if (pattern.test(params.text)) {
        throw new Error('Potentially malicious script content detected in text');
      }
    }
  }

  async validateImageAnalysisInput(params) {
    if (!params.image_url || typeof params.image_url !== 'string') {
      throw new Error('Image analysis requires image_url string');
    }

    // Validate URL format
    try {
      const url = new URL(params.image_url);
      
      // Only allow HTTP/HTTPS
      if (!['http:', 'https:'].includes(url.protocol)) {
        throw new Error('Only HTTP/HTTPS URLs are allowed');
      }

      // Block localhost and private IPs
      if (this.isPrivateOrLocalhost(url.hostname)) {
        throw new Error('Private/localhost URLs are not allowed');
      }

      // Check for suspicious file extensions
      const suspiciousExtensions = ['.exe', '.bat', '.sh', '.php', '.asp', '.jsp'];
      const pathname = url.pathname.toLowerCase();
      
      for (const ext of suspiciousExtensions) {
        if (pathname.endsWith(ext)) {
          throw new Error(`Suspicious file extension: ${ext}`);
        }
      }

    } catch (error) {
      if (error.message.includes('Invalid URL')) {
        throw new Error('Invalid URL format');
      }
      throw error;
    }
  }

  async checkDataExfiltration(result) {
    // Check for suspicious data patterns that might indicate data exfiltration
    const resultString = JSON.stringify(result);
    
    // Check for large base64 encoded data
    const base64Matches = resultString.match(/[A-Za-z0-9+/]{100,}={0,2}/g);
    if (base64Matches && base64Matches.length > 0) {
      console.log(`⚠️ Large base64 data detected in output (${base64Matches.length} matches)`);
      
      // If there's a lot of base64 data, it might be suspicious
      const totalBase64Length = base64Matches.reduce((sum, match) => sum + match.length, 0);
      if (totalBase64Length > 10000) {
        throw new Error('Suspicious amount of base64 data in output');
      }
    }

    // Check for potential file paths or system information
    const systemPatterns = [
      /\/etc\/passwd/,
      /\/proc\/\w+/,
      /C:\\Windows\\/,
      /\/home\/\w+/,
      /\/var\/log\//
    ];

    for (const pattern of systemPatterns) {
      if (pattern.test(resultString)) {
        throw new Error('Potential system information leak detected in output');
      }
    }
  }

  containsBase64Data(str) {
    // Simple check for base64 data (at least 20 characters)
    const base64Pattern = /^[A-Za-z0-9+/]{20,}={0,2}$/;
    return base64Pattern.test(str);
  }

  isPrivateOrLocalhost(hostname) {
    // Check for localhost
    if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '::1') {
      return true;
    }

    // Check for private IP ranges
    const privateRanges = [
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
      /^192\.168\./,
      /^169\.254\./ // Link-local
    ];

    return privateRanges.some(range => range.test(hostname));
  }

  getObjectDepth(obj, depth = 0) {
    if (depth > this.maxObjectDepth) {
      return depth;
    }

    if (obj && typeof obj === 'object') {
      if (Array.isArray(obj)) {
        return Math.max(depth, ...obj.map(item => this.getObjectDepth(item, depth + 1)));
      } else {
        return Math.max(depth, ...Object.values(obj).map(value => this.getObjectDepth(value, depth + 1)));
      }
    }

    return depth;
  }

  // Method to check if a provider's results are consistently suspicious
  async analyzeProviderBehavior(providerAddress, recentResults) {
    const suspiciousIndicators = {
      tooFast: 0,
      tooSlow: 0,
      inconsistentResults: 0,
      securityViolations: 0
    };

    for (const result of recentResults) {
      // Check processing times
      if (result.processingTime < 100) {
        suspiciousIndicators.tooFast++;
      } else if (result.processingTime > 30000) {
        suspiciousIndicators.tooSlow++;
      }

      // Check for security violations in history
      if (result.securityViolation) {
        suspiciousIndicators.securityViolations++;
      }
    }

    const totalResults = recentResults.length;
    const suspiciousRatio = (
      suspiciousIndicators.tooFast + 
      suspiciousIndicators.tooSlow + 
      suspiciousIndicators.securityViolations
    ) / totalResults;

    return {
      providerAddress,
      suspiciousRatio,
      indicators: suspiciousIndicators,
      recommendation: suspiciousRatio > 0.3 ? 'INVESTIGATE' : 'OK'
    };
  }
}

module.exports = SecurityChecker;
