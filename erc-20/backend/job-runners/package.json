{"name": "datacoin-job-runners", "version": "1.0.0", "description": "Standardized job runners with <PERSON><PERSON> for DATACOIN anti-fraud system", "main": "orchestrator/job-orchestrator.js", "scripts": {"build": "chmod +x build-images.sh && ./build-images.sh", "test": "node test/test-runner.js", "start": "node orchestrator/job-orchestrator.js", "clean": "docker system prune -f && docker rmi $(docker images 'datacoin/*' -q) 2>/dev/null || true"}, "dependencies": {"lodash": "^4.17.21", "natural": "^6.5.0", "sharp": "^0.32.0", "node-fetch": "^2.6.7", "canvas": "^2.11.0"}, "devDependencies": {"mocha": "^10.0.0", "chai": "^4.3.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["datacoin", "job-runner", "docker", "anti-fraud", "blockchain"], "author": "DATACOIN Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/datacoin/job-runners.git"}}