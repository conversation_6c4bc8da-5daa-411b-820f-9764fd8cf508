{"jobTypes": {"data_processing": {"dockerImage": "datacoin/data-processing:latest", "timeout": 30000, "maxMemory": "512m", "maxCpu": "1", "deterministic": true, "validation": {"resultType": "json", "requiredFields": ["result", "processingTime"], "maxResultSize": 1024}}, "ai_training": {"dockerImage": "datacoin/ai-training:latest", "timeout": 300000, "maxMemory": "2g", "maxCpu": "2", "deterministic": false, "validation": {"resultType": "json", "requiredFields": ["modelPath", "accuracy", "trainingTime"], "maxResultSize": 2048}}, "image_analysis": {"dockerImage": "datacoin/image-analysis:latest", "timeout": 60000, "maxMemory": "1g", "maxCpu": "1", "deterministic": true, "validation": {"resultType": "json", "requiredFields": ["analysis", "confidence"], "maxResultSize": 1024}}, "text_analysis": {"dockerImage": "datacoin/text-analysis:latest", "timeout": 30000, "maxMemory": "512m", "maxCpu": "1", "deterministic": true, "validation": {"resultType": "json", "requiredFields": ["sentiment", "confidence"], "maxResultSize": 512}}, "text_generation": {"dockerImage": "datacoin/text-generation:latest", "timeout": 120000, "maxMemory": "1g", "maxCpu": "1", "deterministic": false, "validation": {"resultType": "json", "requiredFields": ["generatedText", "tokenCount"], "maxResultSize": 4096}}, "image_generation": {"dockerImage": "datacoin/image-generation:latest", "timeout": 180000, "maxMemory": "2g", "maxCpu": "2", "deterministic": false, "validation": {"resultType": "json", "requiredFields": ["imageUrl", "prompt", "generationTime"], "maxResultSize": 1024}}, "speech_to_text": {"dockerImage": "datacoin/speech-to-text:latest", "timeout": 120000, "maxMemory": "1g", "maxCpu": "1", "deterministic": true, "validation": {"resultType": "json", "requiredFields": ["transcription", "confidence"], "maxResultSize": 2048}}}, "security": {"networkMode": "none", "readOnlyRootfs": true, "noNewPrivileges": true, "dropCapabilities": ["ALL"], "tmpfsSize": "100m"}}