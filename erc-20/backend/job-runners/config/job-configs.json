{"jobTypes": {"data_processing": {"dockerImage": "datacoin/data-processing:latest", "timeout": 30000, "maxMemory": "512m", "maxCpu": "1", "deterministic": true, "validation": {"resultType": "json", "requiredFields": ["result", "processingTime"], "maxResultSize": 1024}}, "ai_training": {"dockerImage": "datacoin/ai-training:latest", "timeout": 300000, "maxMemory": "2g", "maxCpu": "2", "deterministic": false, "validation": {"resultType": "json", "requiredFields": ["status", "trainingTime"], "maxResultSize": 2048}}}, "security": {"networkMode": "none", "readOnlyRootfs": true, "noNewPrivileges": true, "dropCapabilities": ["ALL"], "tmpfsSize": "100m"}}