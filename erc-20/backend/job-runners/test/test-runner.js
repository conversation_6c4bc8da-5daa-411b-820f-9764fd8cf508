const JobOrchestrator = require('../orchestrator/job-orchestrator');

class JobRunnerTester {
  constructor() {
    this.orchestrator = new JobOrchestrator();
    this.testResults = [];
  }

  async runAllTests() {
    console.log('🧪 Starting Job Runner Tests...');
    
    try {
      await this.orchestrator.initialize();
      
      // Test data processing
      await this.testDataProcessing();

      // Test AI training
      await this.testAITraining();
      
      // Print results
      this.printResults();
      
    } catch (error) {
      console.error('❌ Test initialization failed:', error.message);
    } finally {
      await this.orchestrator.cleanup();
    }
  }

  async testDataProcessing() {
    console.log('\n📊 Testing Data Processing...');
    
    const testCases = [
      {
        name: 'Sum operation',
        params: { numbers: [1, 2, 3, 4, 5], operation: 'sum' },
        expected: 15
      },
      {
        name: 'Average operation',
        params: { numbers: [10, 20, 30], operation: 'average' },
        expected: 20
      },
      {
        name: 'Max operation',
        params: { numbers: [5, 10, 3, 8], operation: 'max' },
        expected: 10
      }
    ];

    for (const testCase of testCases) {
      try {
        console.log(`  🔄 Testing: ${testCase.name}`);
        
        const result = await this.orchestrator.executeJob(
          `test-${Date.now()}`,
          'data_processing',
          testCase.params
        );

        if (result.success && result.result.result === testCase.expected) {
          console.log(`  ✅ ${testCase.name}: PASSED`);
          this.testResults.push({ test: testCase.name, status: 'PASSED' });
        } else {
          console.log(`  ❌ ${testCase.name}: FAILED - Expected ${testCase.expected}, got ${result.result?.result}`);
          this.testResults.push({ test: testCase.name, status: 'FAILED', error: 'Unexpected result' });
        }
        
      } catch (error) {
        console.log(`  ❌ ${testCase.name}: ERROR - ${error.message}`);
        this.testResults.push({ test: testCase.name, status: 'ERROR', error: error.message });
      }
    }
  }

  async testAITraining() {
    console.log('\n🤖 Testing AI Training...');

    const testCases = [
      {
        name: 'Basic neural network training',
        params: {
          dataset: 'mnist',
          epochs: 10,
          learningRate: 0.001,
          modelType: 'neural_network'
        }
      },
      {
        name: 'Decision tree training',
        params: {
          dataset: 'iris',
          epochs: 5,
          learningRate: 0.01,
          modelType: 'decision_tree'
        }
      },
      {
        name: 'High epoch training',
        params: {
          dataset: 'cifar10',
          epochs: 100,
          learningRate: 0.0001,
          modelType: 'neural_network'
        }
      }
    ];

    for (const testCase of testCases) {
      try {
        console.log(`  🔄 Testing: ${testCase.name}`);

        const result = await this.orchestrator.executeJob(
          `test-${Date.now()}`,
          'ai_training',
          testCase.params
        );

        if (result.success && result.result.status === 'completed' && result.result.accuracy) {
          console.log(`  ✅ ${testCase.name}: PASSED (Accuracy: ${result.result.accuracy})`);
          this.testResults.push({ test: testCase.name, status: 'PASSED' });
        } else {
          console.log(`  ❌ ${testCase.name}: FAILED - Missing status or accuracy`);
          this.testResults.push({ test: testCase.name, status: 'FAILED', error: 'Missing required fields' });
        }

      } catch (error) {
        console.log(`  ❌ ${testCase.name}: ERROR - ${error.message}`);
        this.testResults.push({ test: testCase.name, status: 'ERROR', error: error.message });
      }
    }
  }

  printResults() {
    console.log('\n📋 Test Results Summary:');
    console.log('========================');
    
    const passed = this.testResults.filter(r => r.status === 'PASSED').length;
    const failed = this.testResults.filter(r => r.status === 'FAILED').length;
    const errors = this.testResults.filter(r => r.status === 'ERROR').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`🚨 Errors: ${errors}`);
    console.log(`📊 Total: ${this.testResults.length}`);
    
    if (failed > 0 || errors > 0) {
      console.log('\n❌ Failed/Error Tests:');
      this.testResults
        .filter(r => r.status !== 'PASSED')
        .forEach(r => {
          console.log(`  - ${r.test}: ${r.status} ${r.error ? `(${r.error})` : ''}`);
        });
    }
    
    console.log('\n' + (failed === 0 && errors === 0 ? '🎉 All tests passed!' : '⚠️ Some tests failed.'));
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new JobRunnerTester();
  tester.runAllTests().catch(console.error);
}

module.exports = JobRunnerTester;
