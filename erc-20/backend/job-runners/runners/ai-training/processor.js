const _ = require('lodash');

class AITrainingRunner {
  constructor() {
    this.startTime = Date.now();
  }

  async processJob(params) {
    try {
      console.log('🤖 Starting AI training job...');
      console.log('📥 Input params:', JSON.stringify(params));

      // Validate input
      if (!params || typeof params !== 'object') {
        throw new Error('Invalid params: must be an object');
      }

      const { 
        dataset = 'default',
        epochs = 10,
        learningRate = 0.001,
        batchSize = 32,
        modelType = 'neural_network'
      } = params;

      // Validate parameters
      if (typeof epochs !== 'number' || epochs < 1 || epochs > 1000) {
        throw new Error('Invalid epochs: must be a number between 1 and 1000');
      }

      if (typeof learningRate !== 'number' || learningRate <= 0 || learningRate > 1) {
        throw new Error('Invalid learning rate: must be a number between 0 and 1');
      }

      if (typeof batchSize !== 'number' || batchSize < 1 || batchSize > 1024) {
        throw new Error('Invalid batch size: must be a number between 1 and 1024');
      }

      const validModelTypes = ['neural_network', 'decision_tree', 'random_forest', 'svm', 'linear_regression'];
      if (!validModelTypes.includes(modelType)) {
        throw new Error(`Invalid model type: ${modelType}. Must be one of: ${validModelTypes.join(', ')}`);
      }

      const processingStartTime = Date.now();

      // Simulate training process
      const result = await this.simulateTraining({
        dataset,
        epochs,
        learningRate,
        batchSize,
        modelType
      });

      const processingTime = Date.now() - processingStartTime;

      const output = {
        ...result,
        dataset: dataset,
        epochs: epochs,
        learningRate: learningRate,
        batchSize: batchSize,
        modelType: modelType,
        processingTime: processingTime,
        timestamp: new Date().toISOString()
      };

      console.log('✅ AI training completed successfully');
      console.log('📤 Output:', JSON.stringify(output));

      return output;

    } catch (error) {
      console.error('❌ AI training failed:', error.message);
      throw error;
    }
  }

  async simulateTraining(config) {
    const { epochs, learningRate, batchSize, modelType } = config;
    
    console.log(`🔄 Training ${modelType} model for ${epochs} epochs...`);
    
    // Simulate training time (more epochs = more time)
    const baseTime = 1000; // 1 second base
    const timePerEpoch = 100; // 100ms per epoch
    const trainingTime = baseTime + (epochs * timePerEpoch);
    
    // Simulate training delay
    await this.sleep(Math.min(trainingTime, 10000)); // Max 10 seconds for demo
    
    // Calculate simulated accuracy based on parameters
    let baseAccuracy = 0.7;
    
    // Better learning rate gives better accuracy
    if (learningRate >= 0.001 && learningRate <= 0.01) {
      baseAccuracy += 0.1;
    }
    
    // More epochs generally improve accuracy (with diminishing returns)
    const epochBonus = Math.min(epochs / 100, 0.15);
    baseAccuracy += epochBonus;
    
    // Model type affects accuracy
    const modelAccuracyBonus = {
      'neural_network': 0.05,
      'random_forest': 0.03,
      'decision_tree': 0.01,
      'svm': 0.02,
      'linear_regression': 0.0
    };
    baseAccuracy += modelAccuracyBonus[modelType] || 0;
    
    // Add some randomness
    const randomFactor = (Math.random() - 0.5) * 0.1; // ±5%
    const finalAccuracy = Math.max(0.5, Math.min(0.99, baseAccuracy + randomFactor));
    
    // Calculate model size based on complexity
    const complexityFactor = {
      'neural_network': 3.0,
      'random_forest': 2.0,
      'decision_tree': 1.0,
      'svm': 1.5,
      'linear_regression': 0.5
    };
    const modelSize = (complexityFactor[modelType] * epochs * 0.1).toFixed(1);
    
    // Calculate loss (inversely related to accuracy)
    const finalLoss = (1 - finalAccuracy) * Math.random() * 0.5;
    
    return {
      status: 'completed',
      accuracy: Math.round(finalAccuracy * 10000) / 10000, // 4 decimal places
      loss: Math.round(finalLoss * 10000) / 10000,
      trainingTime: trainingTime,
      modelSize: `${modelSize}MB`,
      convergence: epochs > 50 ? 'converged' : 'partial',
      metrics: {
        precision: Math.round((finalAccuracy + Math.random() * 0.05) * 10000) / 10000,
        recall: Math.round((finalAccuracy + Math.random() * 0.05) * 10000) / 10000,
        f1Score: Math.round((finalAccuracy + Math.random() * 0.03) * 10000) / 10000
      }
    };
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Main execution
async function main() {
  try {
    // Read input from environment or stdin
    const input = process.env.JOB_PARAMS || process.argv[2];
    
    if (!input) {
      throw new Error('No input provided. Set JOB_PARAMS environment variable or pass as argument');
    }

    const params = JSON.parse(input);
    const runner = new AITrainingRunner();
    const result = await runner.processJob(params);
    
    // Output result to stdout for orchestrator to capture
    console.log('RESULT:', JSON.stringify(result));
    process.exit(0);

  } catch (error) {
    console.error('FATAL ERROR:', error.message);
    process.exit(1);
  }
}

// Health check endpoint
if (process.argv.includes('--health')) {
  console.log('OK');
  process.exit(0);
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = AITrainingRunner;
