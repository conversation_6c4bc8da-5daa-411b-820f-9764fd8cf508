const _ = require('lodash');

class DataProcessingRunner {
  constructor() {
    this.startTime = Date.now();
  }

  async processJob(params) {
    try {
      console.log('🔄 Starting data processing job...');
      console.log('📥 Input params:', JSON.stringify(params));

      // Validate input
      if (!params || typeof params !== 'object') {
        throw new Error('Invalid params: must be an object');
      }

      const { numbers, operation = 'sum' } = params;

      if (!Array.isArray(numbers)) {
        throw new Error('Invalid params: numbers must be an array');
      }

      if (numbers.length === 0) {
        throw new Error('Invalid params: numbers array cannot be empty');
      }

      // Validate all elements are numbers
      if (!numbers.every(n => typeof n === 'number' && !isNaN(n))) {
        throw new Error('Invalid params: all elements must be valid numbers');
      }

      let result;
      const processingStartTime = Date.now();

      switch (operation) {
        case 'sum':
          result = numbers.reduce((a, b) => a + b, 0);
          break;
        case 'average':
          result = numbers.reduce((a, b) => a + b, 0) / numbers.length;
          break;
        case 'max':
          result = Math.max(...numbers);
          break;
        case 'min':
          result = Math.min(...numbers);
          break;
        case 'median':
          const sorted = [...numbers].sort((a, b) => a - b);
          const mid = Math.floor(sorted.length / 2);
          result = sorted.length % 2 === 0 
            ? (sorted[mid - 1] + sorted[mid]) / 2 
            : sorted[mid];
          break;
        case 'std':
          const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
          const variance = numbers.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / numbers.length;
          result = Math.sqrt(variance);
          break;
        default:
          throw new Error(`Unsupported operation: ${operation}`);
      }

      const processingTime = Date.now() - processingStartTime;

      const output = {
        result: result,
        operation: operation,
        inputCount: numbers.length,
        processingTime: processingTime,
        timestamp: new Date().toISOString()
      };

      console.log('✅ Processing completed successfully');
      console.log('📤 Output:', JSON.stringify(output));

      return output;

    } catch (error) {
      console.error('❌ Processing failed:', error.message);
      throw error;
    }
  }
}

// Main execution
async function main() {
  try {
    // Read input from environment or stdin
    const input = process.env.JOB_PARAMS || process.argv[2];
    
    if (!input) {
      throw new Error('No input provided. Set JOB_PARAMS environment variable or pass as argument');
    }

    const params = JSON.parse(input);
    const runner = new DataProcessingRunner();
    const result = await runner.processJob(params);
    
    // Output result to stdout for orchestrator to capture
    console.log('RESULT:', JSON.stringify(result));
    process.exit(0);

  } catch (error) {
    console.error('FATAL ERROR:', error.message);
    process.exit(1);
  }
}

// Health check endpoint
if (process.argv.includes('--health')) {
  console.log('OK');
  process.exit(0);
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = DataProcessingRunner;
