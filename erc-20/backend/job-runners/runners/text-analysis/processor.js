const natural = require('natural');

class TextAnalysisRunner {
  constructor() {
    this.startTime = Date.now();
    this.analyzer = new natural.SentimentAnalyzer('English', 
      natural.PorterStemmer, ['negation']);
  }

  async processJob(params) {
    try {
      console.log('🔄 Starting text analysis job...');
      console.log('📥 Input params:', JSON.stringify(params));

      // Validate input
      if (!params || typeof params !== 'object') {
        throw new Error('Invalid params: must be an object');
      }

      const { text, analysisType = 'sentiment' } = params;

      if (!text || typeof text !== 'string') {
        throw new Error('Invalid params: text must be a non-empty string');
      }

      if (text.length === 0) {
        throw new Error('Invalid params: text cannot be empty');
      }

      if (text.length > 10000) {
        throw new Error('Invalid params: text too long (max 10000 characters)');
      }

      const processingStartTime = Date.now();
      let result;

      switch (analysisType) {
        case 'sentiment':
          result = await this.analyzeSentiment(text);
          break;
        case 'keywords':
          result = await this.extractKeywords(text);
          break;
        case 'language':
          result = await this.detectLanguage(text);
          break;
        case 'readability':
          result = await this.analyzeReadability(text);
          break;
        default:
          throw new Error(`Unsupported analysis type: ${analysisType}`);
      }

      const processingTime = Date.now() - processingStartTime;

      const output = {
        ...result,
        analysisType: analysisType,
        textLength: text.length,
        processingTime: processingTime,
        timestamp: new Date().toISOString()
      };

      console.log('✅ Analysis completed successfully');
      console.log('📤 Output:', JSON.stringify(output));

      return output;

    } catch (error) {
      console.error('❌ Analysis failed:', error.message);
      throw error;
    }
  }

  async analyzeSentiment(text) {
    // Tokenize and stem
    const tokens = natural.WordTokenizer().tokenize(text.toLowerCase());
    const stemmedTokens = tokens.map(token => natural.PorterStemmer.stem(token));
    
    // Calculate sentiment score
    const score = this.analyzer.getSentiment(stemmedTokens);
    
    // Determine sentiment label
    let sentiment;
    let confidence;
    
    if (score > 0.1) {
      sentiment = 'positive';
      confidence = Math.min(score * 100, 100);
    } else if (score < -0.1) {
      sentiment = 'negative';
      confidence = Math.min(Math.abs(score) * 100, 100);
    } else {
      sentiment = 'neutral';
      confidence = 100 - Math.abs(score) * 100;
    }

    return {
      sentiment: sentiment,
      confidence: Math.round(confidence * 100) / 100,
      score: Math.round(score * 1000) / 1000,
      tokenCount: tokens.length
    };
  }

  async extractKeywords(text) {
    const tokens = natural.WordTokenizer().tokenize(text.toLowerCase());
    const stopwords = natural.stopwords;
    
    // Remove stopwords and short words
    const filteredTokens = tokens.filter(token => 
      !stopwords.includes(token) && token.length > 2
    );
    
    // Count frequency
    const frequency = {};
    filteredTokens.forEach(token => {
      frequency[token] = (frequency[token] || 0) + 1;
    });
    
    // Sort by frequency and take top 10
    const keywords = Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word, count]) => ({ word, count }));

    return {
      keywords: keywords,
      totalTokens: tokens.length,
      uniqueTokens: Object.keys(frequency).length
    };
  }

  async detectLanguage(text) {
    // Simple language detection based on common words
    const englishWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of'];
    const tokens = natural.WordTokenizer().tokenize(text.toLowerCase());
    
    const englishCount = tokens.filter(token => englishWords.includes(token)).length;
    const englishRatio = englishCount / tokens.length;
    
    let language = 'unknown';
    let confidence = 0;
    
    if (englishRatio > 0.1) {
      language = 'english';
      confidence = Math.min(englishRatio * 100, 95);
    } else {
      language = 'other';
      confidence = 50;
    }

    return {
      language: language,
      confidence: Math.round(confidence * 100) / 100,
      englishRatio: Math.round(englishRatio * 1000) / 1000
    };
  }

  async analyzeReadability(text) {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = natural.WordTokenizer().tokenize(text);
    const syllables = words.reduce((total, word) => total + this.countSyllables(word), 0);
    
    // Flesch Reading Ease Score
    const avgSentenceLength = words.length / sentences.length;
    const avgSyllablesPerWord = syllables / words.length;
    const fleschScore = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);
    
    let readabilityLevel;
    if (fleschScore >= 90) readabilityLevel = 'very easy';
    else if (fleschScore >= 80) readabilityLevel = 'easy';
    else if (fleschScore >= 70) readabilityLevel = 'fairly easy';
    else if (fleschScore >= 60) readabilityLevel = 'standard';
    else if (fleschScore >= 50) readabilityLevel = 'fairly difficult';
    else if (fleschScore >= 30) readabilityLevel = 'difficult';
    else readabilityLevel = 'very difficult';

    return {
      fleschScore: Math.round(fleschScore * 100) / 100,
      readabilityLevel: readabilityLevel,
      sentenceCount: sentences.length,
      wordCount: words.length,
      syllableCount: syllables,
      avgSentenceLength: Math.round(avgSentenceLength * 100) / 100,
      avgSyllablesPerWord: Math.round(avgSyllablesPerWord * 100) / 100
    };
  }

  countSyllables(word) {
    word = word.toLowerCase();
    if (word.length <= 3) return 1;
    word = word.replace(/(?:[^laeiouy]es|ed|[^laeiouy]e)$/, '');
    word = word.replace(/^y/, '');
    const matches = word.match(/[aeiouy]{1,2}/g);
    return matches ? matches.length : 1;
  }
}

// Main execution
async function main() {
  try {
    // Read input from environment or stdin
    const input = process.env.JOB_PARAMS || process.argv[2];
    
    if (!input) {
      throw new Error('No input provided. Set JOB_PARAMS environment variable or pass as argument');
    }

    const params = JSON.parse(input);
    const runner = new TextAnalysisRunner();
    const result = await runner.processJob(params);
    
    // Output result to stdout for orchestrator to capture
    console.log('RESULT:', JSON.stringify(result));
    process.exit(0);

  } catch (error) {
    console.error('FATAL ERROR:', error.message);
    process.exit(1);
  }
}

// Health check endpoint
if (process.argv.includes('--health')) {
  console.log('OK');
  process.exit(0);
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = TextAnalysisRunner;
