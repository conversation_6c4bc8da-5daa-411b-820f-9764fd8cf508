const sharp = require('sharp');
const fetch = require('node-fetch');

class ImageAnalysisRunner {
  constructor() {
    this.startTime = Date.now();
  }

  async processJob(params) {
    try {
      console.log('🔄 Starting image analysis job...');
      console.log('📥 Input params:', JSON.stringify(params));

      // Validate input
      if (!params || typeof params !== 'object') {
        throw new Error('Invalid params: must be an object');
      }

      const { image_url, analysisType = 'basic' } = params;

      if (!image_url || typeof image_url !== 'string') {
        throw new Error('Invalid params: image_url must be a non-empty string');
      }

      // Validate URL format
      try {
        new URL(image_url);
      } catch {
        throw new Error('Invalid params: image_url must be a valid URL');
      }

      const processingStartTime = Date.now();
      
      // Download and analyze image
      const imageBuffer = await this.downloadImage(image_url);
      let result;

      switch (analysisType) {
        case 'basic':
          result = await this.basicAnalysis(imageBuffer);
          break;
        case 'metadata':
          result = await this.metadataAnalysis(imageBuffer);
          break;
        case 'colors':
          result = await this.colorAnalysis(imageBuffer);
          break;
        case 'histogram':
          result = await this.histogramAnalysis(imageBuffer);
          break;
        default:
          throw new Error(`Unsupported analysis type: ${analysisType}`);
      }

      const processingTime = Date.now() - processingStartTime;

      const output = {
        ...result,
        analysisType: analysisType,
        imageUrl: image_url,
        processingTime: processingTime,
        timestamp: new Date().toISOString()
      };

      console.log('✅ Analysis completed successfully');
      console.log('📤 Output:', JSON.stringify(output));

      return output;

    } catch (error) {
      console.error('❌ Analysis failed:', error.message);
      throw error;
    }
  }

  async downloadImage(url) {
    try {
      console.log('📥 Downloading image from:', url);
      
      const response = await fetch(url, {
        timeout: 30000,
        headers: {
          'User-Agent': 'DATACOIN-ImageAnalyzer/1.0'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.startsWith('image/')) {
        throw new Error(`Invalid content type: ${contentType}. Expected image/*`);
      }

      const buffer = await response.buffer();
      
      // Check file size (max 10MB)
      if (buffer.length > 10 * 1024 * 1024) {
        throw new Error('Image too large (max 10MB)');
      }

      console.log(`✅ Downloaded image: ${buffer.length} bytes, type: ${contentType}`);
      return buffer;

    } catch (error) {
      throw new Error(`Failed to download image: ${error.message}`);
    }
  }

  async basicAnalysis(imageBuffer) {
    const image = sharp(imageBuffer);
    const metadata = await image.metadata();
    const stats = await image.stats();

    return {
      width: metadata.width,
      height: metadata.height,
      format: metadata.format,
      fileSize: imageBuffer.length,
      channels: metadata.channels,
      hasAlpha: metadata.hasAlpha,
      density: metadata.density,
      colorSpace: metadata.space,
      isAnimated: metadata.pages > 1,
      aspectRatio: Math.round((metadata.width / metadata.height) * 100) / 100,
      megapixels: Math.round((metadata.width * metadata.height) / 1000000 * 100) / 100,
      averageBrightness: Math.round(stats.channels[0].mean),
      confidence: 95
    };
  }

  async metadataAnalysis(imageBuffer) {
    const image = sharp(imageBuffer);
    const metadata = await image.metadata();

    return {
      format: metadata.format,
      width: metadata.width,
      height: metadata.height,
      channels: metadata.channels,
      depth: metadata.depth,
      density: metadata.density,
      chromaSubsampling: metadata.chromaSubsampling,
      isProgressive: metadata.isProgressive,
      hasProfile: !!metadata.icc,
      orientation: metadata.orientation,
      exif: metadata.exif ? 'present' : 'none',
      icc: metadata.icc ? 'present' : 'none',
      iptc: metadata.iptc ? 'present' : 'none',
      xmp: metadata.xmp ? 'present' : 'none',
      confidence: 100
    };
  }

  async colorAnalysis(imageBuffer) {
    const image = sharp(imageBuffer);
    const { width, height } = await image.metadata();
    
    // Resize to smaller size for faster processing
    const resized = await image
      .resize(100, 100, { fit: 'inside' })
      .raw()
      .toBuffer({ resolveWithObject: true });

    const { data, info } = resized;
    const pixels = [];
    
    // Extract RGB values
    for (let i = 0; i < data.length; i += info.channels) {
      pixels.push({
        r: data[i],
        g: data[i + 1],
        b: data[i + 2]
      });
    }

    // Calculate dominant colors
    const colorCounts = {};
    pixels.forEach(pixel => {
      // Quantize colors to reduce variations
      const r = Math.floor(pixel.r / 32) * 32;
      const g = Math.floor(pixel.g / 32) * 32;
      const b = Math.floor(pixel.b / 32) * 32;
      const key = `${r},${g},${b}`;
      colorCounts[key] = (colorCounts[key] || 0) + 1;
    });

    const dominantColors = Object.entries(colorCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([color, count]) => {
        const [r, g, b] = color.split(',').map(Number);
        return {
          rgb: { r, g, b },
          hex: `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`,
          percentage: Math.round((count / pixels.length) * 100 * 100) / 100
        };
      });

    // Calculate average color
    const avgR = Math.round(pixels.reduce((sum, p) => sum + p.r, 0) / pixels.length);
    const avgG = Math.round(pixels.reduce((sum, p) => sum + p.g, 0) / pixels.length);
    const avgB = Math.round(pixels.reduce((sum, p) => sum + p.b, 0) / pixels.length);

    return {
      dominantColors: dominantColors,
      averageColor: {
        rgb: { r: avgR, g: avgG, b: avgB },
        hex: `#${avgR.toString(16).padStart(2, '0')}${avgG.toString(16).padStart(2, '0')}${avgB.toString(16).padStart(2, '0')}`
      },
      totalPixelsAnalyzed: pixels.length,
      uniqueColors: Object.keys(colorCounts).length,
      confidence: 90
    };
  }

  async histogramAnalysis(imageBuffer) {
    const image = sharp(imageBuffer);
    
    // Get histogram for each channel
    const redHist = await image.extractChannel(0).toBuffer();
    const greenHist = await image.extractChannel(1).toBuffer();
    const blueHist = await image.extractChannel(2).toBuffer();

    // Calculate basic statistics
    const calculateStats = (buffer) => {
      const values = Array.from(buffer);
      const sum = values.reduce((a, b) => a + b, 0);
      const mean = sum / values.length;
      const variance = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length;
      const stdDev = Math.sqrt(variance);
      
      return {
        mean: Math.round(mean * 100) / 100,
        stdDev: Math.round(stdDev * 100) / 100,
        min: Math.min(...values),
        max: Math.max(...values)
      };
    };

    return {
      red: calculateStats(redHist),
      green: calculateStats(greenHist),
      blue: calculateStats(blueHist),
      overall: {
        brightness: Math.round(((calculateStats(redHist).mean + calculateStats(greenHist).mean + calculateStats(blueHist).mean) / 3) * 100) / 100,
        contrast: Math.round(((calculateStats(redHist).stdDev + calculateStats(greenHist).stdDev + calculateStats(blueHist).stdDev) / 3) * 100) / 100
      },
      confidence: 85
    };
  }
}

// Main execution
async function main() {
  try {
    // Read input from environment or stdin
    const input = process.env.JOB_PARAMS || process.argv[2];
    
    if (!input) {
      throw new Error('No input provided. Set JOB_PARAMS environment variable or pass as argument');
    }

    const params = JSON.parse(input);
    const runner = new ImageAnalysisRunner();
    const result = await runner.processJob(params);
    
    // Output result to stdout for orchestrator to capture
    console.log('RESULT:', JSON.stringify(result));
    process.exit(0);

  } catch (error) {
    console.error('FATAL ERROR:', error.message);
    process.exit(1);
  }
}

// Health check endpoint
if (process.argv.includes('--health')) {
  console.log('OK');
  process.exit(0);
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = ImageAnalysisRunner;
