# DATACOIN Job-Runner Deployment Guide

## Tổng quan

Hệ thống job-runner chuẩn với Docker được thiết kế để chống gian lận provider trong mạng DATACOIN. Mỗi job được thực thi trong môi trường Docker isolated với các cơ chế validation và fraud detection.

## Kiến trúc

```
┌─────────────────────────────────────────────────────────────┐
│                    DATACOIN Job-Runner System               │
├─────────────────────────────────────────────────────────────┤
│  Job-Listener.js                                           │
│  ├── Nhận job từ blockchain                                │
│  ├── Gọi Job Orchestrator                                  │
│  └── Trả kết quả về smart contract                         │
├─────────────────────────────────────────────────────────────┤
│  Job Orchestrator                                          │
│  ├── Security Checker (Input/Output validation)           │
│  ├── Docker Manager (Container execution)                 │
│  ├── Result Validator (Format & content validation)       │
│  └── Fraud Detector (Anti-fraud analysis)                 │
├─────────────────────────────────────────────────────────────┤
│  Docker Runners (Isolated execution)                       │
│  ├── data-processing:latest                               │
│  ├── text-analysis:latest                                 │
│  ├── image-analysis:latest                                │
│  ├── ai-training:latest                                   │
│  ├── text-generation:latest                               │
│  ├── image-generation:latest                              │
│  └── speech-to-text:latest                                │
└─────────────────────────────────────────────────────────────┘
```

## Cài đặt

### 1. <PERSON><PERSON><PERSON> c<PERSON><PERSON> hệ thống

- **Node.js**: >= 18.0.0
- **Docker**: >= 20.10.0
- **RAM**: >= 4GB (khuyến nghị 8GB+)
- **Disk**: >= 10GB free space
- **OS**: Linux (Ubuntu 20.04+), macOS, Windows với WSL2

### 2. Cài đặt dependencies

```bash
# Clone repository
cd erc-20/backend

# Install main dependencies
npm install

# Install job-runners dependencies
cd job-runners
npm install
cd ..
```

### 3. Cấu hình môi trường

Tạo file `.env` hoặc `.env.local`:

```bash
# Network Configuration
RPC_URL=http://127.0.0.1:8545
CHAIN_ID=9000

# Contract Addresses
JOB_PAYMENT_ADDRESS=******************************************

# Backend Wallet (Provider private key)
BACKEND_PRIVATE_KEY=your_private_key_here

# Processing Configuration
POLLING_INTERVAL=30000
PLATFORM_FEE_PERCENT=5
```

### 4. Build Docker images

```bash
# Make build script executable
chmod +x job-runners/build-images.sh

# Build all Docker images
cd job-runners
./build-images.sh
cd ..
```

### 5. Khởi động hệ thống

#### Option 1: Script tự động
```bash
chmod +x start-with-docker.sh
./start-with-docker.sh
```

#### Option 2: Manual
```bash
# Start job processor
node job-listener.js
```

#### Option 3: Docker Compose (Production)
```bash
cd job-runners
docker-compose up -d
```

## Kiểm tra hoạt động

### 1. Chạy tests
```bash
cd job-runners
npm test
```

### 2. Kiểm tra Docker images
```bash
docker images | grep datacoin
```

### 3. Kiểm tra logs
```bash
# Real-time logs
docker logs -f datacoin-job-orchestrator

# Job processor logs
tail -f logs/job-processor.log
```

## Cơ chế chống gian lận

### 1. Security Validation
- **Input validation**: Kiểm tra tham số đầu vào
- **Output validation**: Kiểm tra kết quả đầu ra
- **Resource limits**: Giới hạn CPU, memory, network
- **Timeout protection**: Thời gian thực thi tối đa

### 2. Fraud Detection
- **Execution time analysis**: Phát hiện thời gian bất thường
- **Result consistency**: Kiểm tra tính nhất quán
- **Pattern analysis**: Phát hiện hành vi bot
- **Cross-validation**: So sánh kết quả giữa providers
- **Statistical analysis**: Phân tích thống kê hành vi

### 3. Docker Isolation
- **Read-only filesystem**: Không thể ghi file
- **No network access**: Cô lập mạng
- **Resource constraints**: Giới hạn tài nguyên
- **Security options**: Tắt privileges

## Monitoring & Debugging

### 1. System Stats
```bash
# Get system statistics
curl http://localhost:3001/api/stats
```

### 2. Provider Reports
```bash
# Get provider fraud report
curl http://localhost:3001/api/provider/0x...
```

### 3. Suspicious Providers
```bash
# List suspicious providers
curl http://localhost:3001/api/suspicious-providers
```

### 4. Benchmark Data
```bash
# Get benchmark data
curl http://localhost:3001/api/benchmarks
```

## Troubleshooting

### 1. Docker issues
```bash
# Check Docker daemon
docker info

# Rebuild images
cd job-runners
./build-images.sh

# Clean up
docker system prune -f
```

### 2. Job execution failures
```bash
# Check container logs
docker logs datacoin-job-orchestrator

# Test individual job types
cd job-runners
npm test
```

### 3. Performance issues
```bash
# Monitor resource usage
docker stats

# Check system resources
htop
df -h
```

## Production Deployment

### 1. Security hardening
- Sử dụng non-root user
- Enable firewall
- Regular security updates
- Monitor logs

### 2. Scaling
- Multiple provider instances
- Load balancing
- Database clustering
- Monitoring alerts

### 3. Backup & Recovery
- Regular database backups
- Configuration backups
- Disaster recovery plan

## API Endpoints

### Job Orchestrator API
- `GET /api/stats` - System statistics
- `GET /api/provider/:address` - Provider report
- `GET /api/suspicious-providers` - Suspicious providers
- `GET /api/benchmarks` - Benchmark data
- `POST /api/job/execute` - Execute job manually
- `DELETE /api/provider/:address/history` - Clear provider history

## Support

Để được hỗ trợ:
1. Kiểm tra logs trong `logs/` directory
2. Chạy diagnostic tests: `npm test`
3. Kiểm tra Docker status: `docker ps`
4. Review configuration files

## Changelog

### v1.0.0
- Initial release với Docker runners
- Fraud detection system
- Security validation
- Cross-validation support
- Monitoring & alerting
