const DockerManager = require('./docker-manager');
const ResultValidator = require('../validation/result-validator');
const SecurityChecker = require('../validation/security-checker');
const FraudDetector = require('../validation/fraud-detector');
const jobConfigs = require('../config/job-configs.json');

class JobOrchestrator {
  constructor() {
    this.dockerManager = new DockerManager();
    this.resultValidator = new ResultValidator();
    this.securityChecker = new SecurityChecker();
    this.fraudDetector = new FraudDetector();
    this.runningJobs = new Map();
  }

  async initialize() {
    console.log('🚀 Initializing Job Orchestrator...');
    await this.dockerManager.initialize();
    console.log('✅ Job Orchestrator initialized');
  }

  async executeJob(jobId, jobType, params, providerAddress = null) {
    const jobKey = `${jobId}_${jobType}`;
    const startTime = Date.now();

    try {
      console.log(`🔄 Starting job execution: ${jobKey}`);

      // Check if job is already running
      if (this.runningJobs.has(jobKey)) {
        throw new Error(`Job ${jobKey} is already running`);
      }

      // Get job configuration
      const config = jobConfigs.jobTypes[jobType];
      if (!config) {
        throw new Error(`Unsupported job type: ${jobType}`);
      }

      // Security check on input parameters
      await this.securityChecker.validateInput(params, jobType);

      // Mark job as running
      this.runningJobs.set(jobKey, {
        startTime: startTime,
        status: 'running',
        jobId,
        jobType,
        providerAddress
      });

      console.log(`🐳 Executing job ${jobKey} with Docker image: ${config.dockerImage}`);

      // Execute job in Docker container
      const result = await this.dockerManager.runJob(jobType, params, config);

      // Validate result
      const validatedResult = await this.resultValidator.validate(result, jobType, config);

      // Security check on output
      await this.securityChecker.validateOutput(validatedResult, jobType);

      const executionTime = Date.now() - startTime;

      // Fraud detection analysis
      let fraudAnalysis = null;
      if (providerAddress) {
        fraudAnalysis = await this.fraudDetector.analyzeExecution(
          providerAddress,
          jobId,
          jobType,
          params,
          { success: true, result: validatedResult },
          executionTime
        );

        if (fraudAnalysis.recommendation === 'BLOCK') {
          console.log(`🚨 Job ${jobKey} blocked due to fraud detection`);
          throw new Error(`Job blocked: ${fraudAnalysis.reasons.join(', ')}`);
        }
      }

      console.log(`✅ Job ${jobKey} completed successfully`);

      // Remove from running jobs
      this.runningJobs.delete(jobKey);

      return {
        success: true,
        result: validatedResult,
        executionTime: executionTime,
        fraudAnalysis: fraudAnalysis,
        jobId,
        jobType
      };

    } catch (error) {
      console.error(`❌ Job ${jobKey} failed:`, error.message);

      const executionTime = Date.now() - startTime;

      // Record failed execution for fraud analysis
      if (providerAddress) {
        await this.fraudDetector.analyzeExecution(
          providerAddress,
          jobId,
          jobType,
          params,
          { success: false, error: error.message },
          executionTime
        );
      }

      // Clean up
      this.runningJobs.delete(jobKey);
      await this.dockerManager.cleanup(jobKey);

      return {
        success: false,
        error: error.message,
        executionTime: executionTime,
        jobId,
        jobType
      };
    }
  }

  async getJobStatus(jobId, jobType) {
    const jobKey = `${jobId}_${jobType}`;
    const job = this.runningJobs.get(jobKey);
    
    if (!job) {
      return { status: 'not_found' };
    }

    return {
      status: job.status,
      startTime: job.startTime,
      runningTime: Date.now() - job.startTime,
      jobId: job.jobId,
      jobType: job.jobType
    };
  }

  async cancelJob(jobId, jobType) {
    const jobKey = `${jobId}_${jobType}`;
    
    try {
      console.log(`🛑 Cancelling job: ${jobKey}`);
      
      if (!this.runningJobs.has(jobKey)) {
        throw new Error(`Job ${jobKey} is not running`);
      }

      // Stop Docker container
      await this.dockerManager.stopJob(jobKey);
      
      // Remove from running jobs
      this.runningJobs.delete(jobKey);
      
      console.log(`✅ Job ${jobKey} cancelled successfully`);
      return { success: true, message: 'Job cancelled' };

    } catch (error) {
      console.error(`❌ Failed to cancel job ${jobKey}:`, error.message);
      return { success: false, error: error.message };
    }
  }

  async getRunningJobs() {
    const jobs = [];
    for (const [jobKey, job] of this.runningJobs.entries()) {
      jobs.push({
        jobKey,
        jobId: job.jobId,
        jobType: job.jobType,
        status: job.status,
        startTime: job.startTime,
        runningTime: Date.now() - job.startTime
      });
    }
    return jobs;
  }

  async cleanup() {
    console.log('🧹 Cleaning up Job Orchestrator...');
    
    // Cancel all running jobs
    const runningJobKeys = Array.from(this.runningJobs.keys());
    for (const jobKey of runningJobKeys) {
      const [jobId, jobType] = jobKey.split('_');
      await this.cancelJob(jobId, jobType);
    }

    // Cleanup Docker manager
    await this.dockerManager.cleanup();
    
    console.log('✅ Job Orchestrator cleanup completed');
  }

  async getSystemStats() {
    const dockerStats = await this.dockerManager.getStats();

    return {
      runningJobs: this.runningJobs.size,
      totalJobsProcessed: dockerStats.totalContainersRun || 0,
      systemResources: dockerStats.systemResources || {},
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      fraudDetection: {
        suspiciousProviders: this.fraudDetector.getAllSuspiciousProviders().length,
        benchmarkData: this.fraudDetector.getBenchmarkData()
      }
    };
  }

  async getProviderReport(providerAddress) {
    return this.fraudDetector.getProviderReport(providerAddress);
  }

  async getAllSuspiciousProviders() {
    return this.fraudDetector.getAllSuspiciousProviders();
  }

  async clearProviderHistory(providerAddress) {
    this.fraudDetector.clearProviderHistory(providerAddress);
    console.log(`🧹 Cleared history for provider: ${providerAddress}`);
  }

  async crossValidateResults(jobType, results) {
    return this.resultValidator.crossValidate(results, jobType);
  }
}

module.exports = JobOrchestrator;
