const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');

class DockerManager {
  constructor() {
    this.runningContainers = new Map();
    this.totalContainersRun = 0;
  }

  async initialize() {
    console.log('🐳 Initializing Docker Manager...');
    
    // Check if Docker is available
    await this.checkDockerAvailability();
    
    // Build Docker images if needed
    await this.buildImages();
    
    console.log('✅ Docker Manager initialized');
  }

  async checkDockerAvailability() {
    return new Promise((resolve, reject) => {
      const docker = spawn('docker', ['--version']);
      
      docker.on('close', (code) => {
        if (code === 0) {
          console.log('✅ Docker is available');
          resolve();
        } else {
          reject(new Error('Docker is not available or not installed'));
        }
      });

      docker.on('error', (error) => {
        reject(new Error(`Docker check failed: ${error.message}`));
      });
    });
  }

  async buildImages() {
    console.log('🔨 Building Docker images...');
    
    const jobTypes = ['data-processing', 'text-analysis', 'image-analysis'];
    
    for (const jobType of jobTypes) {
      try {
        const imageName = `datacoin/${jobType}:latest`;
        const dockerfilePath = path.join(__dirname, '..', 'runners', jobType);
        
        // Check if Dockerfile exists
        try {
          await fs.access(path.join(dockerfilePath, 'Dockerfile'));
        } catch {
          console.log(`⚠️ Dockerfile not found for ${jobType}, skipping...`);
          continue;
        }

        console.log(`🔨 Building image: ${imageName}`);
        await this.buildImage(imageName, dockerfilePath);
        console.log(`✅ Built image: ${imageName}`);
        
      } catch (error) {
        console.error(`❌ Failed to build image for ${jobType}:`, error.message);
      }
    }
  }

  async buildImage(imageName, contextPath) {
    return new Promise((resolve, reject) => {
      const docker = spawn('docker', ['build', '-t', imageName, contextPath], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      let errorOutput = '';

      docker.stdout.on('data', (data) => {
        output += data.toString();
      });

      docker.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      docker.on('close', (code) => {
        if (code === 0) {
          resolve(output);
        } else {
          reject(new Error(`Docker build failed: ${errorOutput}`));
        }
      });

      docker.on('error', (error) => {
        reject(new Error(`Docker build error: ${error.message}`));
      });
    });
  }

  async 
  runJob(jobType, params, config) {
    const containerId = `datacoin-job-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      console.log(`🚀 Starting container: ${containerId}`);
      
      // Prepare Docker run command
      const dockerArgs = [
        'run',
        '--rm',
        '--name', containerId,
        '--memory', config.maxMemory,
        '--cpus', config.maxCpu,
        '--network', 'none', // No network access for security
        '--read-only', // Read-only filesystem
        '--tmpfs', '/tmp:size=100m,noexec,nosuid,nodev',
        '--security-opt', 'no-new-privileges:true',
        '--cap-drop', 'ALL',
        '-e', `JOB_PARAMS=${JSON.stringify(params)}`,
        config.dockerImage
      ];

      const result = await this.executeContainer(containerId, dockerArgs, config.timeout);
      
      this.totalContainersRun++;
      return result;

    } catch (error) {
      // Cleanup container if it exists
      await this.forceRemoveContainer(containerId);
      throw error;
    }
  }

  async executeContainer(containerId, dockerArgs, timeout) {
    return new Promise((resolve, reject) => {
      const docker = spawn('docker', dockerArgs, {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      let errorOutput = '';
      let timeoutHandle;

      // Set timeout
      if (timeout) {
        timeoutHandle = setTimeout(() => {
          docker.kill('SIGKILL');
          reject(new Error(`Job execution timeout after ${timeout}ms`));
        }, timeout);
      }

      // Track running container
      this.runningContainers.set(containerId, {
        process: docker,
        startTime: Date.now()
      });

      docker.stdout.on('data', (data) => {
        output += data.toString();
      });

      docker.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      docker.on('close', (code) => {
        if (timeoutHandle) clearTimeout(timeoutHandle);
        this.runningContainers.delete(containerId);

        if (code === 0) {
          // Extract result from output
          const resultMatch = output.match(/RESULT: (.+)/);
          if (resultMatch) {
            try {
              const result = JSON.parse(resultMatch[1]);
              resolve(result);
            } catch (parseError) {
              reject(new Error(`Failed to parse job result: ${parseError.message}`));
            }
          } else {
            reject(new Error('No result found in job output'));
          }
        } else {
          reject(new Error(`Job execution failed with code ${code}: ${errorOutput}`));
        }
      });

      docker.on('error', (error) => {
        if (timeoutHandle) clearTimeout(timeoutHandle);
        this.runningContainers.delete(containerId);
        reject(new Error(`Docker execution error: ${error.message}`));
      });
    });
  }

  async stopJob(jobKey) {
    // Find container by job key (this is simplified, in real implementation you'd track this better)
    for (const [containerId, container] of this.runningContainers.entries()) {
      if (containerId.includes(jobKey)) {
        console.log(`🛑 Stopping container: ${containerId}`);
        container.process.kill('SIGTERM');
        
        // Force kill after 5 seconds if not stopped
        setTimeout(() => {
          if (this.runningContainers.has(containerId)) {
            container.process.kill('SIGKILL');
          }
        }, 5000);
        
        break;
      }
    }
  }

  async forceRemoveContainer(containerId) {
    try {
      await new Promise((resolve) => {
        const docker = spawn('docker', ['rm', '-f', containerId]);
        docker.on('close', () => resolve());
        docker.on('error', () => resolve()); // Ignore errors
      });
    } catch (error) {
      // Ignore cleanup errors
      console.log(`⚠️ Failed to cleanup container ${containerId}:`, error.message);
    }
  }

  async cleanup(jobKey = null) {
    console.log('🧹 Cleaning up Docker containers...');
    
    if (jobKey) {
      // Cleanup specific job
      await this.stopJob(jobKey);
    } else {
      // Cleanup all running containers
      const containerIds = Array.from(this.runningContainers.keys());
      for (const containerId of containerIds) {
        await this.forceRemoveContainer(containerId);
      }
      this.runningContainers.clear();
    }
  }

  async getStats() {
    return {
      runningContainers: this.runningContainers.size,
      totalContainersRun: this.totalContainersRun,
      systemResources: await this.getSystemResources()
    };
  }

  async getSystemResources() {
    try {
      return new Promise((resolve) => {
        const docker = spawn('docker', ['system', 'df', '--format', 'json']);
        let output = '';
        
        docker.stdout.on('data', (data) => {
          output += data.toString();
        });
        
        docker.on('close', () => {
          try {
            const stats = JSON.parse(output);
            resolve(stats);
          } catch {
            resolve({});
          }
        });
        
        docker.on('error', () => resolve({}));
      });
    } catch {
      return {};
    }
  }
}

module.exports = DockerManager;
