#!/bin/bash

# Startup script for DATACOIN Job Processor with Docker runners

set -e

echo "🚀 Starting DATACOIN Job Processor with Docker Runners..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! command_exists docker; then
        echo -e "${RED}❌ Docker is not installed${NC}"
        echo "Please install Docker first: https://docs.docker.com/get-docker/"
        exit 1
    fi

    if ! docker info >/dev/null 2>&1; then
        echo -e "${RED}❌ Docker daemon is not running${NC}"
        echo "Please start Docker daemon first"
        exit 1
    fi

    echo -e "${GREEN}✅ Docker is available and running${NC}"
}

# Function to check Node.js
check_node() {
    if ! command_exists node; then
        echo -e "${RED}❌ Node.js is not installed${NC}"
        echo "Please install Node.js 18+ first"
        exit 1
    fi

    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        echo -e "${RED}❌ Node.js version is too old (${NODE_VERSION}). Please upgrade to Node.js 18+${NC}"
        exit 1
    fi

    echo -e "${GREEN}✅ Node.js $(node --version) is available${NC}"
}

# Function to install dependencies
install_dependencies() {
    echo -e "${YELLOW}📦 Installing dependencies...${NC}"
    
    # Install main backend dependencies
    if [ -f "package.json" ]; then
        npm install
        echo -e "${GREEN}✅ Main dependencies installed${NC}"
    fi
    
    # Install job-runners dependencies
    if [ -f "job-runners/package.json" ]; then
        cd job-runners
        npm install
        cd ..
        echo -e "${GREEN}✅ Job-runners dependencies installed${NC}"
    fi
}

# Function to build Docker images
build_docker_images() {
    echo -e "${YELLOW}🔨 Building Docker images...${NC}"
    
    if [ -f "job-runners/build-images.sh" ]; then
        cd job-runners
        chmod +x build-images.sh
        ./build-images.sh
        cd ..
        echo -e "${GREEN}✅ Docker images built${NC}"
    else
        echo -e "${YELLOW}⚠️ build-images.sh not found, skipping Docker image build${NC}"
    fi
}

# Function to run tests
run_tests() {
    echo -e "${YELLOW}🧪 Running tests...${NC}"
    
    if [ -f "job-runners/test/test-runner.js" ]; then
        cd job-runners
        npm test
        cd ..
        echo -e "${GREEN}✅ Tests completed${NC}"
    else
        echo -e "${YELLOW}⚠️ Tests not found, skipping${NC}"
    fi
}

# Function to start the job processor
start_job_processor() {
    echo -e "${BLUE}🚀 Starting Job Processor...${NC}"
    
    # Check if .env file exists
    if [ ! -f ".env" ] && [ ! -f ".env.local" ]; then
        echo -e "${YELLOW}⚠️ No .env file found. Please create one with the required configuration.${NC}"
        echo "Required variables:"
        echo "  - RPC_URL"
        echo "  - JOB_PAYMENT_ADDRESS"
        echo "  - BACKEND_PRIVATE_KEY"
        echo ""
    fi
    
    # Start the job processor
    echo -e "${GREEN}🎯 Job Processor is starting...${NC}"
    echo -e "${BLUE}📝 Logs will appear below:${NC}"
    echo "----------------------------------------"
    
    node job-listener.js
}

# Main execution
main() {
    echo -e "${BLUE}🔧 DATACOIN Job Processor Setup${NC}"
    echo "=================================="
    
    # Pre-flight checks
    check_docker
    check_node
    
    # Setup
    install_dependencies
    build_docker_images
    
    # Optional: Run tests
    if [ "$1" = "--test" ] || [ "$1" = "-t" ]; then
        run_tests
    fi
    
    # Start the processor
    start_job_processor
}

# Handle script arguments
case "$1" in
    --help|-h)
        echo "DATACOIN Job Processor Startup Script"
        echo ""
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --test, -t    Run tests before starting"
        echo "  --help, -h    Show this help message"
        echo ""
        echo "Environment Variables:"
        echo "  RPC_URL              Blockchain RPC endpoint"
        echo "  JOB_PAYMENT_ADDRESS  JobPayment contract address"
        echo "  BACKEND_PRIVATE_KEY  Provider private key"
        echo "  POLLING_INTERVAL     Job polling interval (default: 30000ms)"
        echo ""
        exit 0
        ;;
    --test|-t)
        main --test
        ;;
    *)
        main
        ;;
esac
