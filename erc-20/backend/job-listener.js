require('dotenv').config();
const { ethers } = require("ethers");
const JobOrchestrator = require('./job-runners/orchestrator/job-orchestrator');

// Dynamic Config from Environment
const RPC_URL = process.env.RPC_URL;
const JOB_PAYMENT_ADDRESS = process.env.JOB_PAYMENT_ADDRESS;
const PRIVATE_KEY = process.env.BACKEND_PRIVATE_KEY;
// const PROVIDER_ADDRESSES = process.env.PROVIDER_ADDRESSES?.split(',') || [];
const POLLING_INTERVAL = parseInt(process.env.POLLING_INTERVAL) || 30000;

const JOB_PAYMENT_ABI = [
  "function assignJob(uint256 jobId, address provider) external",
  "function completeJob(uint256 jobId, bool success, string result) external",
  "function nextJobId() view returns (uint256)",
  "function jobs(uint256) view returns (uint256 id, address requester, address provider, string jobType, uint256 payment, uint8 status, uint256 createdAt, uint256 completedAt, string params)",
  "event JobRequested(uint256 indexed jobId, address indexed requester, string jobType, uint256 payment)",
  "event JobAssigned(uint256 indexed jobId, address indexed provider)",
  "event JobCompleted(uint256 indexed jobId, bool success)"
];

class JobProcessor {
  constructor() {
    this.provider = new ethers.JsonRpcProvider(RPC_URL);
    this.wallet = new ethers.Wallet(PRIVATE_KEY, this.provider);
    this.contract = new ethers.Contract(JOB_PAYMENT_ADDRESS, JOB_PAYMENT_ABI, this.wallet);
    this.processingJobs = new Map();
    this.eventListener = null;
    this.failedAttempts = new Map();
    this.jobOrchestrator = new JobOrchestrator();
    // this.providerPool = PROVIDER_ADDRESSES;
    // this.currentProviderIndex = 0;
  }

  // Dynamic provider selection
  // selectProvider(jobType) {
  //   // Round-robin selection
  //   const provider = this.providerPool[this.currentProviderIndex];
  //   this.currentProviderIndex = (this.currentProviderIndex + 1) % this.providerPool.length;

  //   console.log(`🎯 Selected provider: ${provider} for job type: ${jobType}`);
  //   return provider;
  // }

  async start() {
    console.log("🚀 Job Processor started...");

    // Initialize job orchestrator
    await this.jobOrchestrator.initialize();

    // Process existing pending jobs
    await this.processExistingJobs();

    // Setup event listener
    this.setupEventListener();

    // Backup: Poll for new jobs every 30 seconds
    this.startPolling();

    console.log("👂 Listening for job events...");
  }

  startPolling() {
    setInterval(async () => {
      console.log("🔄 Polling for new jobs...");
      await this.processExistingJobs();
    }, POLLING_INTERVAL);
  }

  setupEventListener() {
    try {
      // Remove old listener if exists
      if (this.eventListener) {
        this.contract.off("JobRequested", this.eventListener);
      }

      // Create new listener with more logging
      this.eventListener = async (jobId, requester, jobType, payment, event) => {
        console.log(`📝 EVENT RECEIVED: New job requested`);
        console.log(`   Job ID: ${jobId}`);
        console.log(`   Requester: ${requester}`);
        console.log(`   Type: ${jobType}`);
        console.log(`   Payment: ${ethers.formatEther(payment)} DATACOIN`);
        console.log(`   Block: ${event.blockNumber}`);

        await this.processNewJob(jobId, jobType);
      };

      this.contract.on("JobRequested", this.eventListener);
      console.log("✅ Event listener setup complete");

      // Test if provider is connected
      this.provider.getBlockNumber().then(blockNumber => {
        console.log(`🔗 Connected to block: ${blockNumber}`);
      }).catch(err => {
        console.error("❌ Provider connection failed:", err);
      });

    } catch (error) {
      console.error("❌ Error setting up event listener:", error);
      setTimeout(() => this.setupEventListener(), 5000);
    }
  }

  async processExistingJobs() {
    try {
      console.log("🔍 Checking for existing pending jobs...");

      const nextJobId = await this.contract.nextJobId();
      console.log(`Next job ID: ${nextJobId}`);

      for (let i = 1; i < nextJobId; i++) {
        try {
          const job = await this.contract.jobs(i);
          console.log(`Job ${i}: Status=${job.status}, Type=${job.jobType}, ID=${job.id}`);

          // Status 0 = Pending
          if (job.status == 0) {
            console.log(`🔄 Found pending job ${i}, processing...`);
            await this.processNewJob(Number(job.id), job.jobType);
          } else {
            console.log(`Job ${i} already processed (status=${job.status})`);
          }
        } catch (error) {
          console.log(`❌ Error checking job ${i}:`, error.message);
        }
      }
    } catch (error) {
      console.error("❌ Error processing existing jobs:", error);
    }
  }

  async processNewJob(jobId, jobType) {
    const jobKey = Number(jobId);

    try {
      if (this.processingJobs.has(jobKey)) {
        console.log(`⚠️ Job ${jobKey} already being processed, skipping...`);
        return;
      }

      this.processingJobs.set(jobKey, true);
      console.log(`🔄 Processing job ${jobKey} of type ${jobType}`);

      const job = await this.contract.jobs(jobKey);
      console.log(`🔍 Job ${jobKey} current status: ${job.status}`);

      if (Number(job.status) != 0) {
        console.log(`⚠️ Job ${jobKey} already processed (status=${job.status}), skipping...`);
        this.processingJobs.delete(jobKey);
        return;
      }

      // Dynamic provider selection
      const providerAddress = await this.wallet.getAddress();

      console.log(`🔄 Assigning job ${jobKey} to provider ${providerAddress}`);
      const assignTx = await this.contract.assignJob(jobKey, providerAddress);
      await assignTx.wait();

      console.log(`✅ Job ${jobKey} assigned successfully`);
      this.startJobProcessing(jobKey, jobType);

    } catch (error) {
      if (error.message.includes("tx already in mempool")) {
        console.log(`⚠️ Job ${jobKey} transaction already in progress, skipping...`);
      } else {
        console.error(`❌ Error assigning job ${jobKey}:`, error.message);
      }

      const attempts = this.failedAttempts.get(jobKey) || 0;
      this.failedAttempts.set(jobKey, attempts + 1);

      if (attempts + 1 >= 3) { // Giới hạn số lần thử là 3
        console.log(`❌ Job ${jobKey} failed after ${attempts + 1} attempts, marking as Failed`);
        await this.completeJob(jobKey, false, ''); // Đánh dấu job là Failed
      }

      this.processingJobs.delete(jobKey);
    }
  }

  startJobProcessing(jobId, jobType) {
    console.log(`⚡ Starting processing job ${jobId} (${jobType})`);

    this.processJob(jobId, jobType)
    .then(async (result) => {
      console.log(`✅ Job ${jobId} processed successfully with result: ${result}`);
      await this.completeJob(jobId, true, result); // true = success
    })
    .catch(async (error) => {
      console.error(`❌ Job ${jobId} failed: ${error.message}`);
      await this.completeJob(jobId, false, error.message); // false = failed
    });
  }

  async processJob(jobId, jobType) {
    try {
      // Get job parameters from contract
      const job = await this.contract.jobs(jobId);
      const params = JSON.parse(job.params || '{}');
      const providerAddress = await this.wallet.getAddress();

      console.log(`🔄 Processing job ${jobId} with Docker runner...`);

      // Use job orchestrator to execute job in Docker
      const result = await this.jobOrchestrator.executeJob(jobId, jobType, params, providerAddress);

      if (result.success) {
        console.log(`✅ Job ${jobId} completed with Docker runner`);

        // Log fraud analysis if available
        if (result.fraudAnalysis) {
          console.log(`🔍 Fraud analysis - Score: ${result.fraudAnalysis.fraudScore}, Recommendation: ${result.fraudAnalysis.recommendation}`);
          if (result.fraudAnalysis.suspicious) {
            console.log(`⚠️ Suspicious activity detected: ${result.fraudAnalysis.reasons.join(', ')}`);
          }
        }

        return JSON.stringify(result.result);
      } else {
        throw new Error(result.error);
      }

    } catch (error) {
      console.error(`❌ Docker job execution failed for ${jobId}:`, error.message);

      // Fallback to legacy processing for backward compatibility
      console.log(`🔄 Falling back to legacy processing for job ${jobId}`);
      return await this.processJobLegacy(jobId, jobType);
    }
  }

  async processJobLegacy(jobId, jobType) {
    switch (jobType) {
      case 'data_processing': return await this.handleDataProcessing(jobId);
      case 'ai_training': return await this.handleAITraining(jobId);
      case 'image_analysis': return await this.handleImageAnalysis(jobId);
      case 'text_analysis': return await this.handleTextAnalysis(jobId);
      case 'text_generation': return await this.handleTextGeneration(jobId);
      case 'image_generation': return await this.handleImageGeneration(jobId);
      case 'speech_to_text': return await this.handleSpeechToText(jobId);
      default: throw new Error(`Unsupported job type: ${jobType}`);
    }
  }

  async handleDataProcessing(jobId) {
    const job = await this.contract.jobs(jobId);
    const params = JSON.parse(job.params || '{}');
    const numbers = params.numbers;
    if (!Array.isArray(numbers)) throw new Error("Missing 'numbers' array");
    const sum = numbers.reduce((a, b) => a + b, 0);
    return `Sum: ${sum}`;
  }

  async handleAITraining(jobId) {
    return `Simulated AI training complete`;
  }

  async handleImageAnalysis(jobId) {
    const job = await this.contract.jobs(jobId);
    const params = JSON.parse(job.params || '{}');
    const url = params.image_url;
    if (!url) throw new Error("Missing image_url param");
    const res = await fetch(url);
    if (!res.ok) throw new Error("Invalid image url");
    return `Image Content-Type: ${res.headers.get("content-type")}`;
  }

  async handleTextAnalysis(jobId) {
    const job = await this.contract.jobs(jobId);
    const params = JSON.parse(job.params || '{}');
    const text = params.text || '';
    if (!text) throw new Error("Missing text param");
    const result = text.includes("good") ? "positive" : text.includes("bad") ? "negative" : "neutral";
    return `Sentiment: ${result}`;
  }

  async handleTextGeneration(jobId) {
    const job = await this.contract.jobs(jobId);
    const params = JSON.parse(job.params || '{}');
    const prompt = params.prompt || '';
    return `Generated response from prompt: "${prompt}"`;
  }

  async handleImageGeneration(jobId) {
    const job = await this.contract.jobs(jobId);
    const params = JSON.parse(job.params || '{}');
    const prompt = params.prompt || '';
    return `Simulated image generated for prompt: "${prompt}"`;
  }

  async handleSpeechToText(jobId) {
    const job = await this.contract.jobs(jobId);
    const params = JSON.parse(job.params || '{}');
    const audioUrl = params.audio_url;
    if (!audioUrl) throw new Error("Missing audio_url param");
    return `Transcribed audio from URL: ${audioUrl}`;
  }

  getProcessingTime(jobType) {
    const times = {
      'data_processing': 5000,   // 5 seconds
      'ai_training': 15000,      // 15 seconds
      'image_analysis': 8000,    // 8 seconds
      'text_analysis': 6000      // 6 seconds
    };
    return times[jobType] || 10000;
  }

  async completeJob(jobId, success, result = '') {
    try {
      console.log(`🏁 Completing job ${jobId} with result: ${success ? 'SUCCESS' : 'FAILED'}`);

      const completeTx = await this.contract.completeJob(jobId, success, result);
      await completeTx.wait();

      console.log(`✅ Job ${jobId} completed and payment settled`);

      // Remove from processing map
      this.processingJobs.delete(jobId);

    } catch (error) {
      console.error(`❌ Error completing job ${jobId}:`, error);
      this.processingJobs.delete(jobId);
    }
  }
}

// Start the processor
const processor = new JobProcessor();
processor.start().catch(console.error);
