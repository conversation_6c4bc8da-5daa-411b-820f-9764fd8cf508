{"name": "my-erc20-project", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "deploy-job": "npx hardhat ignition deploy ignition/modules/JobPayment.js --network ethermint"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^6.1.0", "hardhat": "^2.26.1"}, "dependencies": {"@openzeppelin/contracts": "^4.9.5", "dotenv": "^16.0.0"}}