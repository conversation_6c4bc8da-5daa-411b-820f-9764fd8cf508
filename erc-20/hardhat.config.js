require('dotenv').config();
require("@nomicfoundation/hardhat-toolbox");

/** @type import('hardhat/config').HardhatUserConfig */
module.exports = {
  solidity: "0.8.28",
  networks: {
    ethermint: {
      url: process.env.RPC_URL || "http://127.0.0.1:8545", // Ethermint JSON-RPC endpoint
      chainId: 9000, // Chain ID từ init.sh
      accounts: [
        process.env.BACKEND_PRIVATE_KEY
      ],
      gas: ********,
      gasPrice: **********
    },
    localhost: {
      url: "http://127.0.0.1:9545",
      chainId: 31337
    }
  }
};
